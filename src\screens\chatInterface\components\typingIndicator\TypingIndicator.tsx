import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Image } from 'react-native';
import { Colors } from '../../../../constants/Colors';

export interface TypingIndicatorProps {
  isTyping: boolean;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ isTyping }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    if (isTyping) {
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Exit animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 20,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isTyping, fadeAnim, slideAnim]);

  if (!isTyping) return null;

  return (
    <View style={styles.typingContainer}>
      <Animated.View
        style={[
          styles.typingSurface,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.typingContent}>
          <View style={styles.avatarContainer}>
            <Image
              source={require('../../../../../assets/chat-avatar.png')}
              style={styles.botAvatar}
              resizeMode="cover"
            />
          </View>
          <View style={styles.typingBubble}>
            <View style={styles.typingDotsContainer}>
              <View style={styles.typingDot} />
              <View style={[styles.typingDot, styles.typingDotDelay1]} />
              <View style={[styles.typingDot, styles.typingDotDelay2]} />
            </View>
          </View>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  typingContainer: {
    // backgroundColor: 'green',
    marginHorizontal: 16,
    marginVertical: 8,
  },
  typingSurface: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  typingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  avatarContainer: {
    marginRight: 12,
  },
  botAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  typingBubble: {
    // backgroundColor: Colors.botMessage,
    // backgroundColor:'yellow',
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minWidth: 60,
    
  },
  typingDotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.textSecondary,
    marginHorizontal: 2,
    opacity: 0.4,
  },
  typingDotDelay1: {
    // Animation delay would be handled by parent component
  },
  typingDotDelay2: {
    // Animation delay would be handled by parent component
  },
});

export default TypingIndicator;
