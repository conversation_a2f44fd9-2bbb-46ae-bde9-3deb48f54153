import { fileService } from '../fileService';

// Mock the expo-file-system module
jest.mock('expo-file-system', () => ({
  File: {
    downloadFileAsync: jest.fn().mockResolvedValue({
      uri: 'file:///mock/path/test.pdf',
      exists: true,
      size: 1024
    })
  },
  Directory: jest.fn(),
  Paths: {
    cache: 'file:///mock/cache/'
  }
}));

jest.mock('expo-sharing', () => ({
  isAvailableAsync: jest.fn().mockResolvedValue(true),
  shareAsync: jest.fn().mockResolvedValue(undefined)
}));

describe('FileService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('downloadAndSaveFile', () => {
    it('should download a file successfully using modern API', async () => {
      const mockUrl = 'https://example.com/test.pdf';
      const mockFilename = 'test.pdf';

      const result = await fileService.downloadAndSaveFile(mockUrl, mockFilename);

      expect(result).toBe('file:///mock/path/test.pdf');
    });

    it('should handle download errors gracefully', async () => {
      const mockUrl = 'https://invalid-url.com/test.pdf';
      const mockFilename = 'test.pdf';

      // Mock the downloadFileAsync to throw an error
      const { File } = require('expo-file-system');
      File.downloadFileAsync.mockRejectedValueOnce(new Error('Network error'));

      // Mock the legacy fallback
      const FileSystem = require('expo-file-system');
      FileSystem.downloadAsync = jest.fn().mockResolvedValue({
        status: 200,
        uri: 'file:///fallback/path/test.pdf'
      });

      const result = await fileService.downloadAndSaveFile(mockUrl, mockFilename);

      expect(result).toBe('file:///fallback/path/test.pdf');
    });
  });

  describe('shareFile', () => {
    it('should share a file successfully', async () => {
      const mockUri = 'file:///path/to/file.pdf';
      const mockFilename = 'file.pdf';

      await fileService.shareFile(mockUri, mockFilename);

      const { shareAsync } = require('expo-sharing');
      expect(shareAsync).toHaveBeenCalledWith(mockUri, expect.objectContaining({
        mimeType: 'application/pdf',
        dialogTitle: 'Share file.pdf'
      }));
    });
  });

  describe('getMimeType', () => {
    it('should return correct MIME types for different file extensions', () => {
      const testCases = [
        { filename: 'test.pdf', expected: 'application/pdf' },
        { filename: 'test.xlsx', expected: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
        { filename: 'test.xls', expected: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
        { filename: 'test.csv', expected: 'text/csv' },
        { filename: 'test.txt', expected: 'text/plain' },
        { filename: 'test.unknown', expected: 'application/octet-stream' }
      ];

      testCases.forEach(({ filename, expected }) => {
        const mimeType = (fileService as any).getMimeType(filename);
        expect(mimeType).toBe(expected);
      });
    });
  });
});