/**
 * ModernInputBar Component
 * ChatGPT-inspired floating input bar with gradient send button
 * Optimized for both iOS and Android
 */

import React, { memo, useState, useCallback } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../../theme';

interface ModernInputBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  placeholder?: string;
  disabled?: boolean;
}

export const ModernInputBar = memo<ModernInputBarProps>(
  ({ value, onChangeText, onSend, placeholder = 'Message FinanceGPT...', disabled = false }) => {
    const [isFocused, setIsFocused] = useState(false);

    const handleSend = useCallback(() => {
      if (value.trim().length === 0 || disabled) return;

      // Haptic feedback
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      onSend();
    }, [value, disabled, onSend]);

    const canSend = value.trim().length > 0 && !disabled;

    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <View style={styles.container}>
          <View
            style={[
              styles.inputContainer,
              isFocused && styles.inputContainerFocused,
            ]}
          >
            <TextInput
              style={styles.input}
              value={value}
              onChangeText={onChangeText}
              placeholder={placeholder}
              placeholderTextColor={Colors.inputPlaceholder}
              multiline
              maxLength={2000}
              editable={!disabled}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              returnKeyType="default"
              blurOnSubmit={false}
            />
            <TouchableOpacity
              onPress={handleSend}
              disabled={!canSend}
              style={styles.sendButtonContainer}
              activeOpacity={0.8}
            >
              {canSend ? (
                <LinearGradient
                  colors={Colors.userMessageGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.sendButton}
                >
                  <Ionicons name="arrow-up" size={20} color={Colors.textOnPrimary} />
                </LinearGradient>
              ) : (
                <View style={styles.sendButtonDisabled}>
                  <Ionicons name="arrow-up" size={20} color={Colors.textMuted} />
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    );
  }
);

ModernInputBar.displayName = 'ModernInputBar';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.inputBg,
    borderRadius: BorderRadius['2xl'],
    borderWidth: 1,
    borderColor: Colors.inputBorder,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minHeight: 48,
    maxHeight: 120,
    ...Platform.select({
      ios: {
        ...Shadows.lg,
        shadowColor: Colors.shadowMedium,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  inputContainerFocused: {
    borderColor: Colors.inputFocusBorder,
  },
  input: {
    flex: 1,
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    color: Colors.textPrimary,
    paddingTop: 0,
    paddingBottom: 0,
    marginRight: Spacing.sm,
    ...Platform.select({
      ios: {
        paddingTop: 2,
      },
      android: {
        paddingTop: 0,
        textAlignVertical: 'center',
      },
    }),
  },
  sendButtonContainer: {
    marginBottom: Platform.OS === 'android' ? 2 : 0,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
  },
});

