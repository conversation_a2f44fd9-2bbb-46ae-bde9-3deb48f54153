/**
 * TypewriterText Component
 * Animates text character by character to simulate typing effect
 * Optimized for React Native with configurable speed and callbacks
 */

import React, { useState, useEffect, useRef, memo } from 'react';
import { Text, TextStyle } from 'react-native';

interface TypewriterTextProps {
  text: string;
  style?: TextStyle | TextStyle[];
  speed?: number; // Characters per second
  onComplete?: () => void;
  onCharacterTyped?: (currentText: string, progress: number) => void;
  startDelay?: number; // Delay before starting animation in ms
  cursorVisible?: boolean;
  cursorStyle?: TextStyle;
  testID?: string;
}

export const TypewriterText = memo<TypewriterTextProps>(({
  text,
  style,
  speed = 30, // Default 30 characters per second
  onComplete,
  onCharacterTyped,
  startDelay = 0,
  cursorVisible = false,
  cursorStyle,
  testID,
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const [showCursor, setShowCursor] = useState(cursorVisible);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cursorIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0);

  // Calculate delay between characters based on speed
  const charDelay = 1000 / speed;

  useEffect(() => {
    // Reset state when text changes
    setDisplayedText('');
    setIsComplete(false);
    currentIndexRef.current = 0;
    
    // Clear any existing intervals
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Start typing animation after delay
    timeoutRef.current = setTimeout(() => {
      startTyping();
    }, startDelay);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
      }
    };
  }, [text, speed, startDelay]);

  // Cursor blinking effect
  useEffect(() => {
    if (cursorVisible && !isComplete) {
      cursorIntervalRef.current = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500); // Blink every 500ms

      return () => {
        if (cursorIntervalRef.current) {
          clearInterval(cursorIntervalRef.current);
        }
      };
    } else {
      setShowCursor(false);
    }
  }, [cursorVisible, isComplete]);

  const startTyping = () => {
    if (!text) {
      setIsComplete(true);
      onComplete?.();
      return;
    }

    intervalRef.current = setInterval(() => {
      const currentIndex = currentIndexRef.current;
      
      if (currentIndex >= text.length) {
        // Animation complete
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
        setIsComplete(true);
        setShowCursor(false);
        onComplete?.();
        return;
      }

      // Add next character
      const nextChar = text[currentIndex];
      const newText = text.substring(0, currentIndex + 1);
      
      setDisplayedText(newText);
      currentIndexRef.current = currentIndex + 1;

      // Call progress callback
      const progress = (currentIndex + 1) / text.length;
      onCharacterTyped?.(newText, progress);

      // Add slight randomness to typing speed for more natural feel
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        const randomDelay = charDelay + (Math.random() - 0.5) * (charDelay * 0.3);
        
        intervalRef.current = setTimeout(() => {
          startTyping();
        }, Math.max(randomDelay, 10)); // Minimum 10ms delay
      }
    }, charDelay);
  };

  // Skip animation and show full text immediately
  const skipAnimation = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (cursorIntervalRef.current) {
      clearInterval(cursorIntervalRef.current);
    }
    
    setDisplayedText(text);
    setIsComplete(true);
    setShowCursor(false);
    onComplete?.();
  };

  // Note: useImperativeHandle would be used with forwardRef if needed
  // For now, we'll handle this through props and callbacks

  const cursorChar = showCursor ? '|' : '';

  return (
    <Text 
      style={style} 
      testID={testID}
      selectable={isComplete} // Only allow selection when animation is complete
    >
      {displayedText}
      {cursorVisible && (
        <Text style={[style, cursorStyle, { opacity: showCursor ? 1 : 0 }]}>
          {cursorChar}
        </Text>
      )}
    </Text>
  );
});

TypewriterText.displayName = 'TypewriterText';

export default TypewriterText;
