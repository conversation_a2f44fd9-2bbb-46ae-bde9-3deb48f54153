{"cli": {"version": ">= 12.0.0", "appVersionSource": "local"}, "build": {"development": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "developmentClient": true, "distribution": "internal", "channel": "development", "env": {"EXPO_USE_FAST_RESOLVER": "1"}}, "preview": {"android": {"buildType": "apk"}, "developmentClient": false, "distribution": "internal"}, "production": {"android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "distribution": "store", "channel": "production"}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-services-key.json", "track": "internal"}}}}