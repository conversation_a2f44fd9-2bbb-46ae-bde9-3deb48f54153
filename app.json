{"expo": {"name": "FinanceGPT", "slug": "FinanceGPT2", "version": "2.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "cover", "backgroundColor": "#000000"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.financegpt.mobile", "buildNumber": "2.0.0", "infoPlist": {"NSDocumentPickerUsageDescription": "This app needs access to documents to upload files for analysis.", "UISupportsDocumentBrowser": true, "LSSupportsOpeningDocumentsInPlace": true, "NSPhotoLibraryUsageDescription": "This app does not access your photo library.", "NSCameraUsageDescription": "This app does not access your camera.", "NSMicrophoneUsageDescription": "This app does not access your microphone.", "NSLocationWhenInUseUsageDescription": "This app does not access your location.", "ITSAppUsesNonExemptEncryption": false}, "usesAppleSignIn": false, "associatedDomains": []}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false, "package": "com.financegpt.mobile", "versionCode": 3, "permissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "INTERNET", "ACCESS_NETWORK_STATE"], "intentFilters": [{"action": "android.intent.action.VIEW", "category": ["android.intent.category.DEFAULT"], "data": {"mimeType": "application/pdf"}}, {"action": "android.intent.action.VIEW", "category": ["android.intent.category.DEFAULT"], "data": {"mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], "expo-font", "expo-dev-client"], "runtimeVersion": {"policy": "appVersion"}, "extra": {"eas": {"projectId": "55cbd334-6321-48cb-9245-232065678525"}}}}