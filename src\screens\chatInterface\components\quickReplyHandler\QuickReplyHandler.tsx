import React, { useCallback } from 'react';
import { IMessage } from 'react-native-gifted-chat';
import { apiService } from '../../../../services/api';
import { unifiedLogger } from '../../../../services/unifiedLogger';

export interface QuickReplyHandlerProps {
  onSend: (messages: IMessage[]) => Promise<void>;
  showSnackbar: (message: string) => void;
  setHasDownloadableData: (hasData: boolean) => void;
  setCurrentDownloadData: (data: any) => void;
  onNewSession?: () => void;
  handleFileOperation: (
    operation: 'open' | 'share' | 'download',
    downloadUrl: string,
    filename: string,
    format: string,
    setIsProcessingFile: (processing: boolean) => void
  ) => Promise<void>;
  isProcessingFile: boolean;
  setIsProcessingFile: (processing: boolean) => void;
}

export const useQuickReplyHandler = ({
  onSend,
  showSnackbar,
  setHasDownloadableData,
  setCurrentDownloadData,
  onNewSession,
  handleFileOperation,
  isProcessingFile,
  setIsProcessingFile,
}: QuickReplyHandlerProps) => {
  const handleQuickReply = useCallback(async (quickReply: any) => {
    console.log('════════════════════════════════════════');
    console.log('🔄 QUICK REPLY HANDLER - START');
    console.log('🔄 Quick reply received:', quickReply);
    console.log('🔄 Quick reply value:', quickReply?.value);
    console.log('🔄 Quick reply type:', typeof quickReply);
    console.log('🔄 Quick reply keys:', Object.keys(quickReply || {}));
    console.log('════════════════════════════════════════');

    // Handle array format (GiftedChat sometimes passes array)
    const reply = Array.isArray(quickReply) ? quickReply[0] : quickReply;
    console.log('🔄 Processed reply:', reply);

    if (!reply || !reply.value) {
      console.log('❌ No valid quick reply value found');
      return;
    }

    // Handle file operations (open/share/download)
    if (reply.value.startsWith('open_file|||') || reply.value.startsWith('share_file|||') || reply.value.startsWith('download_file|||')) {
      console.log('════════════════════════════════════════');
      console.log('🎯 FILE OPERATION DETECTED!');
      console.log('════════════════════════════════════════');
      try {
        // Prevent multiple simultaneous operations
        if (isProcessingFile) {
          console.log('⏳ File operation already in progress, ignoring...');
          showSnackbar('⏳ Please wait, processing previous file...');
          return;
        }

        // Determine operation type
        let operation: 'open' | 'share' | 'download';
        let prefix: string;

        if (reply.value.startsWith('open_file|||')) {
          operation = 'open';
          prefix = 'open_file|||';
          console.log('📱 OPERATION TYPE: OPEN');
        } else if (reply.value.startsWith('download_file|||')) {
          operation = 'download';
          prefix = 'download_file|||';
          console.log('📥 OPERATION TYPE: DOWNLOAD');
        } else {
          operation = 'share';
          prefix = 'share_file|||';
          console.log('📤 OPERATION TYPE: SHARE');
        }

        // Extract file information using the new separator format
        // Format: operation_file|||downloadUrl|||filename|||format
        const dataString = reply.value.substring(prefix.length);
        const parts = dataString.split('|||');

        if (parts.length !== 3) {
          console.error('❌ Invalid quick reply format - expected 3 parts, got:', parts.length);
          showSnackbar('❌ Invalid file data format');
          return;
        }

        const [downloadUrl, filename, format] = parts;

        console.log('════════════════════════════════════════');
        console.log('🔍 URL PARSING DETAILS:');
        console.log('   Original value:', reply.value);
        console.log('   Prefix:', prefix);
        console.log('   Data string:', dataString);
        console.log('   Parts:', parts);
        console.log('   Extracted URL:', downloadUrl);
        console.log('   Extracted filename:', filename);
        console.log('   Extracted format:', format);
        console.log('════════════════════════════════════════');

        console.log(`📁 Calling handleFileOperation with operation: ${operation}`);

        // Handle all file operations through the file service
        await handleFileOperation(operation, downloadUrl, filename, format, setIsProcessingFile);
      } catch (error) {
        console.error(`❌ Failed to process ${reply.value.startsWith('open_file_') ? 'open' : 'share'} request:`, error);
        unifiedLogger.error('QuickReplyHandler', 'file_operation_failed', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        showSnackbar('❌ Failed to process file request');
        setIsProcessingFile(false);
      }
      return;
    }

    // Handle new session
    if (reply.value === 'new_session') {
      try {
        await apiService.resetSession();
        unifiedLogger.info('QuickReplyHandler', 'new_session_started');
        showSnackbar('New session started');
        onNewSession?.();
      } catch (error) {
        unifiedLogger.error('QuickReplyHandler', 'new_session_error');
        showSnackbar('Failed to start new session');
      }
    } else if (reply.value === 'continue_chat') {
      // Clear download data to allow new queries
      setHasDownloadableData(false);
      setCurrentDownloadData(null);
    } else if (reply.value === 'test_button') {
      console.log('🧪 Test button clicked!');
      showSnackbar('🧪 Test button works!');
    } else {
      // Debug: Log unhandled quick replies
      console.log('🤔 Unhandled quick reply:', reply);
    }
  }, [
    isProcessingFile,
    setIsProcessingFile,
    handleFileOperation,
    showSnackbar,
    setHasDownloadableData,
    setCurrentDownloadData,
    onNewSession,
  ]);

  return {
    handleQuickReply,
  };
};

export default useQuickReplyHandler;
