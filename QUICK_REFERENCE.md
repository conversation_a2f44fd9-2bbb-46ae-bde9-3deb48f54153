# FinanceGPT - Quick Reference Guide

## 🚀 Quick Start

### Start Development Server
```bash
cd FinanceGPT2
npx expo start
```

### Start with Clean Cache
```bash
npx expo start -c
```

### Install Dependencies
```bash
npm install
```

---

## 📱 Testing on Device

### Using Expo Go
1. Install Expo Go from App Store (iOS) or Play Store (Android)
2. Run `npx expo start`
3. Scan the QR code with your device
4. App will load in Expo Go

### Troubleshooting
If you see errors:
1. **Completely close** Expo Go app (swipe away from recent apps)
2. **Clear cache:** `npx expo start -c`
3. **Reopen** Expo Go and scan QR code again

---

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── modern/         # Modern chat UI components
│   ├── loadingIndicator/
│   └── financeGPTTheme/
├── screens/            # App screens
│   ├── chatInterface/  # Main chat screen
│   └── introScreen/    # Splash screen
├── services/           # Business logic
│   ├── api.ts         # API communication
│   ├── fileService.ts # File operations
│   └── fileDownload.ts
├── navigation/         # Navigation setup
├── constants/          # App constants
├── theme/             # Theme configuration
└── types/             # TypeScript types
```

---

## 🎨 Key Components

### ModernTypingIndicator
Animated typing indicator with pulsing dots
```typescript
import { ModernTypingIndicator } from './components/modern';

<ModernTypingIndicator showAvatar={true} />
```

### LoadingIndicator
Full-screen loading overlay
```typescript
import { LoadingIndicator } from './components';

<LoadingIndicator visible={isLoading} message="Loading..." />
```

### FilePreviewCard
Preview card for uploaded files
```typescript
import { FilePreviewCard } from './components/modern';

<FilePreviewCard
  fileName="document.pdf"
  fileSize={1024000}
  onRemove={() => {}}
/>
```

---

## 🔧 Configuration Files

### app.json
Main Expo configuration
- App name, version, icons
- iOS and Android settings
- Permissions

### package.json
Dependencies and scripts
- React Native 0.81.4
- Expo SDK 54
- 25 total dependencies

### babel.config.js
Babel configuration
- Uses `babel-preset-expo`
- No additional plugins needed

---

## 📦 Key Dependencies

### Core
- `expo` - Expo SDK
- `react` - React library
- `react-native` - React Native framework

### Navigation
- `@react-navigation/native` - Navigation library
- `@react-navigation/stack` - Stack navigator
- `react-native-screens` - Native screen components
- `react-native-gesture-handler` - Gesture handling

### UI
- `react-native-paper` - Material Design components
- `react-native-gifted-chat` - Chat UI components
- `expo-linear-gradient` - Gradient backgrounds
- `expo-blur` - Blur effects

### File Handling
- `expo-document-picker` - Document picker
- `expo-file-system` - File system access
- `expo-sharing` - File sharing
- `expo-intent-launcher` - Open files with system apps

### Utilities
- `axios` - HTTP client
- `@react-native-async-storage/async-storage` - Local storage
- `@react-native-community/netinfo` - Network status

---

## 🎯 Common Tasks

### Add a New Screen
1. Create screen component in `src/screens/`
2. Add to navigation in `src/navigation/AppNavigator.tsx`
3. Export from `src/screens/index.ts`

### Add a New Component
1. Create component in `src/components/`
2. Export from `src/components/index.ts`
3. Import where needed

### Update Theme
Edit `src/theme/colors.ts` or `src/constants/Colors.ts`

### Add API Endpoint
Edit `src/services/api.ts`

---

## 🐛 Common Issues & Solutions

### Issue: App won't load
**Solution:**
```bash
npx expo start -c
# Then completely restart Expo Go app
```

### Issue: Module not found
**Solution:**
```bash
npm install
npx expo start -c
```

### Issue: TypeScript errors
**Solution:**
Check `tsconfig.json` and ensure all imports are correct

### Issue: File operations not working
**Solution:**
- Check permissions in `app.json`
- Verify file paths are correct
- Test on physical device (not simulator)

---

## 📱 File Operations

### Download File
```typescript
import { fileService } from './services/fileService';

await fileService.downloadAndSaveFile(url, fileName);
```

### Open File
```typescript
await fileService.openFile(fileUri, fileName);
```

### Share File
```typescript
await fileService.shareFile(fileUri, fileName);
```

---

## 🎨 Theme Colors

```typescript
Colors.primary = '#1a1a2e'
Colors.secondary = '#16213e'
Colors.accent = '#0f3460'
Colors.background = '#0a0e27'
Colors.surface = '#1a1a2e'
Colors.userMessageBg = '#2a5298'
Colors.botMessageBg = '#1e1e2e'
```

---

## 🚢 Building for Production

### Android (APK)
```bash
eas build --platform android --profile preview
```

### iOS (IPA)
```bash
eas build --platform ios --profile preview
```

### Both Platforms
```bash
eas build --platform all --profile production
```

---

## 📊 Performance Tips

1. **Use memo for expensive components**
   ```typescript
   export const MyComponent = memo(() => { ... });
   ```

2. **Optimize images**
   - Use appropriate sizes
   - Compress before adding to assets

3. **Lazy load screens**
   - Use React.lazy() for code splitting

4. **Minimize re-renders**
   - Use useCallback and useMemo
   - Avoid inline functions in render

---

## 🔐 Environment Variables

Create `.env` file in root:
```
API_URL=https://your-api-url.com
API_KEY=your-api-key
```

Access in code:
```typescript
import Constants from 'expo-constants';
const apiUrl = Constants.expoConfig?.extra?.apiUrl;
```

---

## 📝 Git Workflow

```bash
# Create feature branch
git checkout -b feature/my-feature

# Make changes and commit
git add .
git commit -m "Add my feature"

# Push to remote
git push origin feature/my-feature

# Create pull request on GitHub
```

---

## 🎓 Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [React Navigation](https://reactnavigation.org/)
- [React Native Paper](https://callstack.github.io/react-native-paper/)

---

## 📞 Support

For issues or questions:
1. Check this guide first
2. Review `PROJECT_CLEANUP_SUMMARY.md`
3. Check Expo documentation
4. Search GitHub issues

---

**Last Updated:** 2025-10-01
**Version:** 5.0.0
**Status:** ✅ Production Ready

