import React, { useCallback } from 'react';
import { IMessage } from 'react-native-gifted-chat';
import { fileService } from '../../../../services/fileService';
import { unifiedLogger } from '../../../../services/unifiedLogger';
import { Config } from '../../../../constants/Config';

export interface FileAttachmentHandlerProps {
  onFileAttachment: (message: IMessage) => void;
  showSnackbar: (message: string) => void;
}

export const useFileAttachmentHandler = ({
  onFileAttachment,
  showSnackbar,
}: FileAttachmentHandlerProps) => {
  // WhatsApp-style file attachment - show actual file in chat that can be tapped
  const createFileAttachment = useCallback(async (
    serverFilePath: string, 
    filename: string, 
    format: string, 
    sessionId: string
  ) => {
    try {
      console.log('📱 Creating file attachment like WhatsApp:', { serverFilePath, filename, format });
      
      // Map xlsx to excel for backend compatibility
      const backendFormat = format === 'xlsx' ? 'excel' : format;
      
      // Use proper mobile API download endpoint
      let downloadUrl: string;
      if (serverFilePath && serverFilePath.startsWith('http')) {
        // If it's already a full URL, use it directly
        downloadUrl = serverFilePath;
      } else if (serverFilePath && serverFilePath.includes('/download/')) {
        // If it contains download path, use it as-is
        downloadUrl = `${Config.API_BASE_URL}${serverFilePath}`;
      } else {
        // Use session-based download endpoint (fixed path)
        downloadUrl = `${Config.API_BASE_URL}/download/${sessionId}?format=${backendFormat}`;
      }
      
      console.log('🌐 File available at:', downloadUrl);
      console.log('🔧 Creating quick reply values:', {
        openValue: `open_file_${downloadUrl}_${filename}_${format}`,
        shareValue: `share_file_${downloadUrl}_${filename}_${format}`
      });

      // Create WhatsApp-style file attachment message
      const fileMessage: IMessage = {
        _id: Math.random().toString(),
        text: `📎 ${filename}`,
        createdAt: new Date(),
        user: {
          _id: 2,
          name: 'FinanceGPT',
          avatar: '🤖',
        },
        // Add custom properties for file handling
        quickReplies: {
          type: 'radio',
          keepIt: true,
          values: [
            {
              title: `📥 Download ${format.toUpperCase()}`,
              value: `download_file|||${downloadUrl}|||${filename}|||${format}`,
              messageId: Math.random().toString(),
            },
            {
              title: `📤 Share ${format.toUpperCase()}`,
              value: `share_file|||${downloadUrl}|||${filename}|||${format}`,
              messageId: Math.random().toString(),
            },
          ],
        },
      };

      onFileAttachment(fileMessage);
      
      console.log('✅ File attachment created successfully!');
      showSnackbar(`📎 ${format.toUpperCase()} file ready`);
      
    } catch (error) {
      console.error('❌ Failed to create file attachment:', error);
      showSnackbar(`❌ Could not attach ${format.toUpperCase()} file`);
    }
  }, [onFileAttachment, showSnackbar]);

  // Handle file operations (open/share/download)
  const handleFileOperation = useCallback(async (
    operation: 'open' | 'share' | 'download',
    downloadUrl: string,
    filename: string,
    format: string,
    setIsProcessingFile: (processing: boolean) => void
  ) => {
    try {
      console.log('════════════════════════════════════════');
      console.log(`🚀 FILE ATTACHMENT HANDLER - ${operation.toUpperCase()} OPERATION`);
      console.log('   Download URL:', downloadUrl);
      console.log('   Filename:', filename);
      console.log('   Format:', format);
      console.log('════════════════════════════════════════');
      setIsProcessingFile(true);

      if (operation === 'download') {
        console.log('📥 DOWNLOAD PATH: Saving to user storage...');
        // For download, save to user-accessible location
        await fileService.downloadToUserStorage(downloadUrl, filename);
        console.log('✅ File downloaded to user storage');
        showSnackbar('✅ File downloaded successfully');
        unifiedLogger.logFileOperation('FileAttachmentHandler', 'file_downloaded', { filename });
      } else {
        // For open/share, download to cache first
        console.log('════════════════════════════════════════');
        console.log('📥 STEP 1: Downloading file to cache...');
        console.log('   URL:', downloadUrl);
        console.log('════════════════════════════════════════');
        const localFilePath = await fileService.downloadAndSaveFile(downloadUrl, filename);

        if (!localFilePath) {
          throw new Error('Failed to download file');
        }

        console.log('✅ File downloaded to cache:', localFilePath);

        // Perform the requested operation
        if (operation === 'open') {
          console.log('════════════════════════════════════════');
          console.log('📱 STEP 2: Opening file with system apps...');
          console.log('   Local path:', localFilePath);
          console.log('   Filename:', filename);
          console.log('════════════════════════════════════════');
          await fileService.openFile(localFilePath, filename);
          console.log('✅ File opened successfully');
          showSnackbar('✅ File opened successfully');
          unifiedLogger.logFileOperation('FileAttachmentHandler', 'file_opened', { filename });
        } else if (operation === 'share') {
          console.log('════════════════════════════════════════');
          console.log('📤 STEP 2: Sharing file via share sheet...');
          console.log('   Local path:', localFilePath);
          console.log('   Filename:', filename);
          console.log('════════════════════════════════════════');
          await fileService.shareFile(localFilePath, filename);
          console.log('✅ File shared successfully');
          showSnackbar('✅ File shared successfully');
          unifiedLogger.logFileOperation('FileAttachmentHandler', 'file_shared', { filename });
        }
      }

    } catch (error) {
      console.error(`❌ Failed to process file ${operation} request:`, error);
      unifiedLogger.error('FileAttachmentHandler', `file_${operation}_process_failed`, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      showSnackbar(`❌ Failed to process ${operation} request`);
    } finally {
      setIsProcessingFile(false);
    }
  }, [showSnackbar]);

  return {
    createFileAttachment,
    handleFileOperation,
  };
};

export default useFileAttachmentHandler;
