import * as FileSystem from 'expo-file-system/legacy';
import { getContentUriAsync } from 'expo-file-system/legacy';
import * as Sharing from 'expo-sharing';
import * as IntentLauncher from 'expo-intent-launcher';
import * as DocumentPicker from 'expo-document-picker';
import { Alert, Platform, Linking } from 'react-native';

class FileService {
  private downloadsDirectory: string;

  constructor() {
    // Use legacy FileSystem API for compatibility
    this.downloadsDirectory = (FileSystem.cacheDirectory || FileSystem.documentDirectory || '') + 'downloads/';
    console.log('📁 Downloads directory path:', this.downloadsDirectory);
    this.ensureDownloadsDirectory();
  }

  private async ensureDownloadsDirectory(): Promise<void> {
    try {
      console.log('🔍 Ensuring downloads directory exists');

      // Check if directory exists using standard FileSystem API
      const dirInfo = await FileSystem.getInfoAsync(this.downloadsDirectory);
      console.log('📂 Directory exists check:', dirInfo.exists);

      if (!dirInfo.exists) {
        console.log('📁 Creating downloads directory...');
        await FileSystem.makeDirectoryAsync(this.downloadsDirectory, { intermediates: true });
        console.log('✅ Downloads directory created successfully');
      } else {
        console.log('📂 Downloads directory already exists');
      }
    } catch (error) {
      console.error('❌ Failed to ensure downloads directory:', error);
      throw error;
    }
  }

  async downloadAndSaveFile(fileUrl: string, fileName: string): Promise<string> {
    try {
      // Ensure downloads directory exists
      await this.ensureDownloadsDirectory();

      // Create file path using legacy API
      const fileUri = this.downloadsDirectory + fileName;

      // Delete existing file if it exists
      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (fileInfo.exists) {
        console.log('🗑️ Deleting existing file...');
        await FileSystem.deleteAsync(fileUri, { idempotent: true });
      }

      console.log('📥 Downloading file:', fileUrl);
      console.log('📁 Saving to:', fileUri);

      // Download using legacy FileSystem API
      const downloadResult = await FileSystem.downloadAsync(fileUrl, fileUri, {
        headers: {
          'User-Agent': 'FinanceGPT-Mobile/4.0.0'
        }
      });

      console.log('📋 Download response:', downloadResult);
      console.log('📋 Response status:', downloadResult.status);

      // Check if download was successful
      if (downloadResult.status === 200) {
        console.log('✅ Download successful');
      } else {
        throw new Error(`Download failed with status: ${downloadResult.status}`);
      }

      console.log('✅ File downloaded successfully to:', downloadResult.uri);
      return downloadResult.uri;
    } catch (error) {
      console.error('❌ Error downloading file:', error);
      throw error;
    }
  }

  // Cross-platform file sharing - shows sharing options (WhatsApp, Email, etc.)
  // Main file operation handler
  async handleFileOperation(
    operation: 'open' | 'share' | 'download',
    downloadUrl: string,
    filename: string,
    format: string,
    setIsProcessingFile: (processing: boolean) => void
  ): Promise<void> {
    try {
      setIsProcessingFile(true);
      console.log(`🔄 Starting ${operation} operation for ${filename}`);

      if (operation === 'download') {
        // For download, save to user-accessible location
        await this.downloadToUserStorage(downloadUrl, filename);
      } else {
        // For open/share, download to cache first
        const fileUri = await this.downloadAndSaveFile(downloadUrl, filename);

        if (operation === 'open') {
          await this.openFile(fileUri, filename);
        } else if (operation === 'share') {
          await this.shareFile(fileUri, filename);
        }
      }
    } catch (error) {
      console.error(`❌ ${operation} operation failed:`, error);
      throw error;
    } finally {
      setIsProcessingFile(false);
    }
  }

  // Download file via browser (opens URL in system browser which handles download)
  async downloadToUserStorage(downloadUrl: string, filename: string): Promise<void> {
    try {
      console.log('════════════════════════════════════════');
      console.log('📥 DOWNLOAD VIA BROWSER');
      console.log('   URL:', downloadUrl);
      console.log('   Filename:', filename);
      console.log('   Platform:', Platform.OS);
      console.log('════════════════════════════════════════');

      console.log('🌐 Opening download URL in browser...');
      console.log('   Browser will handle download to Downloads folder');

      // Open URL in system browser - browser handles download automatically
      const canOpen = await Linking.canOpenURL(downloadUrl);

      if (!canOpen) {
        throw new Error('Cannot open download URL');
      }

      await Linking.openURL(downloadUrl);

      console.log('✅ Download URL opened in browser');
      console.log('   File will be saved to Downloads folder by browser');
      console.log('════════════════════════════════════════');

    } catch (error) {
      console.log('════════════════════════════════════════');
      console.error('❌❌❌ BROWSER DOWNLOAD FAILED');
      console.error('Error:', error);
      console.log('════════════════════════════════════════');

      Alert.alert(
        '❌ Download Failed',
        error instanceof Error ? error.message : 'Could not open download in browser. Please try again.',
        [{ text: 'OK' }]
      );
      throw error;
    }
  }

  async shareFile(fileUri: string, fileName: string): Promise<void> {
    try {
      console.log('════════════════════════════════════════');
      console.log('📤 FILE SERVICE - SHARE FILE METHOD');
      console.log('   File URI:', fileUri);
      console.log('   File Name:', fileName);
      console.log('════════════════════════════════════════');

      if (await Sharing.isAvailableAsync()) {
        console.log('✅ Sharing API is available');

        const extension = fileName.split('.').pop()?.toLowerCase() || '';
        const mimeType = this.getMimeType(extension);
        console.log('📋 File details:');
        console.log('   Extension:', extension);
        console.log('   MIME Type:', mimeType);

        console.log('🚀 Calling Sharing.shareAsync for SOCIAL SHARING...');
        // Use Sharing.shareAsync WITHOUT UTI for social/messaging apps (WhatsApp, Email, etc.)
        // This shows the share tray with social apps
        await Sharing.shareAsync(fileUri, {
          mimeType: mimeType,
          dialogTitle: `Share via`,
          // No UTI = shows social sharing apps on both platforms
        });

        console.log('✅✅✅ SUCCESS: Share tray shown (WhatsApp, Email, etc.)');
        console.log('════════════════════════════════════════');
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      console.log('════════════════════════════════════════');
      console.error('❌❌❌ SHARE FILE ERROR');
      console.error('Error:', error);
      console.log('════════════════════════════════════════');
      throw error;
    }
  }

  // Cross-platform file opening - shows system tray with apps that can open the file
  async openFile(fileUri: string, fileName?: string): Promise<void> {
    try {
      console.log('════════════════════════════════════════');
      console.log('📖 FILE SERVICE - OPEN FILE METHOD');
      console.log('   File URI:', fileUri);
      console.log('   File Name:', fileName);
      console.log('════════════════════════════════════════');

      const extension = (fileName || fileUri).split('.').pop()?.toLowerCase() || '';
      const mimeType = this.getMimeType(extension);
      console.log('📋 File details:');
      console.log('   Extension:', extension);
      console.log('   MIME Type:', mimeType);
      console.log('   Platform:', Platform.OS);

      console.log('════════════════════════════════════════');
      console.log('📱 OPEN FILE - Showing viewer apps tray');
      console.log('════════════════════════════════════════');

      // Use Sharing API WITH 'public.item' UTI to show "Save to Files" + viewer apps
      // This shows both save options AND viewer apps
      console.log('   UTI: public.item (for saving/opening)');
      console.log('   MIME Type:', mimeType);
      console.log('🚀 Calling Sharing.shareAsync with public.item UTI...');

      await Sharing.shareAsync(fileUri, {
        mimeType: mimeType,
        UTI: 'public.item', // public.item = shows "Save to Files" + viewer apps
        dialogTitle: `Open with`, // Clear title for "Open with" action
      });

      console.log('✅✅✅ SUCCESS: "Open with" tray shown (PDF readers, Excel apps, etc.)');
      console.log('════════════════════════════════════════');

      console.log('✅ OPEN FILE METHOD COMPLETED');
    } catch (error) {
      console.log('════════════════════════════════════════');
      console.error('❌❌❌ OPEN FILE FATAL ERROR');
      console.error('Error:', error);
      console.log('════════════════════════════════════════');
      throw error;
    }
  }

  // Complete download and open flow (direct open)
  async downloadAndOpenFile(fileUrl: string, fileName: string): Promise<void> {
    try {
      console.log('🔄 Starting cross-platform download and open flow for:', fileName);

      // Step 1: Download the file using modern File API
      const localUri = await this.downloadAndSaveFile(fileUrl, fileName);
      console.log('📥 Download completed, file saved to:', localUri);

      // Step 2: Open the file directly (no dialog)
      await this.openFile(localUri, fileName);

      console.log('✅ Cross-platform download and open completed successfully');
    } catch (error) {
      console.error('❌ Download and open failed:', error);
      throw error;
    }
  }

  // Show file options dialog with Open and Share buttons
  private showFileOptions(fileUri: string, fileName: string): void {
    const fileType = fileName.split('.').pop()?.toUpperCase() || 'File';

    Alert.alert(
      `${fileType} Ready! ✅`,
      `${fileName}\n\nChoose what you'd like to do:`,
      [
        {
          text: `📱 Open ${fileType}`,
          onPress: () => this.openFile(fileUri, fileName),
        },
        {
          text: `📤 Share ${fileType}`,
          onPress: () => this.shareFile(fileUri, fileName),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  }

  // New method to handle complete download and share flow
  async downloadAndShareFile(fileUrl: string, fileName: string): Promise<void> {
    try {
      console.log('🔄 Starting download and share flow for:', fileName);
      
      // Download the file
      const localUri = await this.downloadAndSaveFile(fileUrl, fileName);
      
      // Share the file
      await this.shareFile(localUri, fileName);
      
      console.log('✅ Download and share completed successfully');
    } catch (error) {
      console.error('❌ Download and share failed:', error);
      throw error;
    }
  }

  // Handle Excel file with specific options
  async handleExcelFile(url: string, fileName: string): Promise<void> {
    try {
      console.log('📊 Handling Excel file:', { url, fileName });
      
      const localUri = await this.downloadAndSaveFile(url, fileName);
      
      // Show options for Excel files
      Alert.alert(
        'Excel File Ready! 📊',
        'Your Excel file has been downloaded. What would you like to do?',
        [
          {
            text: 'Open in Excel',
            onPress: () => this.openFile(localUri, fileName),
          },
          {
            text: 'Share File',
            onPress: () => this.shareFile(localUri, fileName),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
      
    } catch (error) {
      console.error('❌ Excel file handling failed:', error);
      throw error;
    }
  }

  // Handle PDF file with specific options
  async handlePDFFile(url: string, fileName: string): Promise<void> {
    try {
      console.log('📄 Handling PDF file:', { url, fileName });
      
      const localUri = await this.downloadAndSaveFile(url, fileName);
      
      // Show options for PDF files
      Alert.alert(
        'PDF File Ready! 📄',
        'Your PDF file has been downloaded. What would you like to do?',
        [
          {
            text: 'Open in PDF Viewer',
            onPress: () => this.openFile(localUri, fileName),
          },
          {
            text: 'Share File',
            onPress: () => this.shareFile(localUri, fileName),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
      
    } catch (error) {
      console.error('❌ PDF file handling failed:', error);
      throw error;
    }
  }

  async pickPDF(): Promise<DocumentPicker.DocumentPickerResult> {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
        copyToCacheDirectory: true,
      });

      return result;
    } catch (error) {
      console.error('Error picking PDF:', error);
      throw error;
    }
  }

  async getDownloadedFiles(): Promise<any[]> {
    try {
      // Check if directory exists using legacy API
      const dirInfo = await FileSystem.getInfoAsync(this.downloadsDirectory);

      if (!dirInfo.exists) {
        return [];
      }

      const files = await FileSystem.readDirectoryAsync(this.downloadsDirectory);
      const fileDetails = [];

      for (const fileName of files) {
        const fileUri = this.downloadsDirectory + fileName;
        const fileInfo = await FileSystem.getInfoAsync(fileUri);

        if (fileInfo.exists && !fileInfo.isDirectory) {
          fileDetails.push({
            name: fileName,
            uri: fileUri,
            size: fileInfo.size || 0,
            modificationTime: fileInfo.modificationTime || 0,
          });
        }
      }

      return fileDetails;
    } catch (error) {
      console.error('❌ Error reading downloaded files:', error);
      return [];
    }
  }

  async deleteFile(fileUri: string): Promise<boolean> {
    try {
      await FileSystem.deleteAsync(fileUri, { idempotent: true });
      console.log('🗑️ File deleted successfully:', fileUri);
      return true;
    } catch (error) {
      console.error('❌ Error deleting file:', error);
      return false;
    }
  }

  public getMimeType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'xlsx':
      case 'xls':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';
      default:
        return 'application/octet-stream';
    }
  }

  private getUTI(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'com.adobe.pdf';
      case 'xlsx':
      case 'xls':
        return 'com.microsoft.excel.xls';
      case 'csv':
        return 'public.comma-separated-values-text';
      default:
        return 'public.data';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const fileService = new FileService();