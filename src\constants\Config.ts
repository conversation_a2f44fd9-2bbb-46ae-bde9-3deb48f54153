import { Platform } from 'react-native';

// Determine the correct backend URL based on platform and environment
const getApiBaseUrl = () => {
  // Production backend with custom domain (HTTPS)
  const CUSTOM_DOMAIN_URL = 'https://codesolutionistsapps.com';

  // Fallback to direct public IP (working backend)
  const PUBLIC_API_URL = 'http://***************:9000';

  // For development/testing on emulator
  const LOCAL_URLS = {
    android: 'http://********:9000',  // Android emulator (updated to port 9000)
    ios: 'http://localhost:9000',     // iOS simulator (updated to port 9000)
    web: 'http://localhost:9000'      // Web platform (updated to port 9000)
  };

  // Smart URL selection with health check capability
  const USE_CUSTOM_DOMAIN = true; // Primary: use custom domain
  const USE_PUBLIC_IP = true; // Fallback: use direct public IP

  // For mobile development, prefer custom domain for HTTPS support
  if (Platform.OS === 'android' || Platform.OS === 'ios') {
    if (USE_CUSTOM_DOMAIN) return CUSTOM_DOMAIN_URL;
    if (USE_PUBLIC_IP) return PUBLIC_API_URL;
  }

  // Fallback to local URLs for emulator
  if (Platform.OS === 'android') {
    return LOCAL_URLS.android;
  } else if (Platform.OS === 'ios') {
    return LOCAL_URLS.ios;
  } else {
    return LOCAL_URLS.web;
  }
};

export const Config = {
  // Backend API Configuration
  API_BASE_URL: getApiBaseUrl(),

  // API Endpoints (using unified chat API like WhatsApp/WordPress)
  ENDPOINTS: {
    CHAT: '/chat',  // Use unified chat endpoint with mobile support
    DOWNLOAD: '/download',  // Use unified download endpoint
    HEALTH: '/health',
    PDF_UPLOAD: '/upload',  // Use unified upload endpoint
    SESSION: '/chat',  // Use chat endpoint for session management (mobile sessions)
  },
  
  // Chat Configuration
  MAX_MESSAGE_LENGTH: 1000,
  TYPING_INDICATOR_DELAY: 1000,
  
  // File Configuration
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB (increased from 10MB)
  SUPPORTED_FILE_TYPES: ['pdf', 'xlsx', 'xls', 'csv'],
  
  // Network Configuration
  REQUEST_TIMEOUT: 30000, // 30 seconds timeout (increased for production)
  MAX_RETRIES: 1, // Only 1 retry (reduced from 3 for faster failure)
  RETRY_DELAY: 2000, // 2 seconds before retry
  
  // Session Configuration
  SESSION_STORAGE_KEY: 'financegpt_session_id',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  // UI Configuration
  MESSAGE_LIMIT: 50,
  AUTO_SCROLL_DELAY: 100,
  
  // App Metadata
  APP_NAME: 'FinanceGPT',
  APP_VERSION: '2.0.0', // Updated version
  USER_AGENT: 'FinanceGPT-Mobile/2.0.0',
  DEPARTMENT_NAME: 'Government Finance Department',
} as const;

// Environment-specific configurations
export const Environment = {
  isDevelopment: __DEV__,
  isProduction: !__DEV__,
} as const;