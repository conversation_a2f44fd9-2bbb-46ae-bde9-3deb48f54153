@echo off
echo ================================================================================
echo 🚀 FINANCEGPT2 - PLAYSTORE BUILD SCRIPT
echo ================================================================================

cd /d "C:\Users\<USER>\Desktop\ChatbotAI\MobileApp\FinanceGPT2"

echo 📱 Current Directory: %CD%
echo 🏗️  Building for PlayStore deployment...
echo.

echo ⚡ Checking EAS CLI installation...
call npm list -g @expo/eas-cli >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing EAS CLI globally...
    call npm install -g @expo/eas-cli
    if %errorlevel% neq 0 (
        echo ❌ Failed to install EAS CLI
        pause
        exit /b 1
    )
)
echo ✅ EAS CLI ready

echo.
echo 🔑 Checking EAS login status...
call eas whoami >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔐 Please login to your Expo account:
    call eas login
    if %errorlevel% neq 0 (
        echo ❌ Login failed
        pause
        exit /b 1
    )
)
echo ✅ EAS login confirmed

echo.
echo 📋 Current configuration:
echo    • API URL: http://***************:9000
echo    • Package: com.financegpt.app
echo    • Version: 1.0.0
echo    • New Architecture: Enabled
echo.

echo 🎯 Choose build type:
echo [1] Development Build (for testing)
echo [2] Production Build (for PlayStore)
echo [3] Both (recommended)
echo.
set /p choice="Enter your choice (1/2/3): "

if "%choice%"=="1" goto dev_build
if "%choice%"=="2" goto prod_build
if "%choice%"=="3" goto both_builds
echo ❌ Invalid choice
pause
exit /b 1

:dev_build
echo 🛠️  Building DEVELOPMENT version...
call eas build --platform android --profile development --non-interactive
goto build_complete

:prod_build
echo 🏪 Building PRODUCTION version for PlayStore...
call eas build --platform android --profile production --non-interactive
goto build_complete

:both_builds
echo 🛠️  Building DEVELOPMENT version first...
call eas build --platform android --profile development --non-interactive
if %errorlevel% neq 0 (
    echo ❌ Development build failed
    pause
    exit /b 1
)

echo 🏪 Building PRODUCTION version for PlayStore...
call eas build --platform android --profile production --non-interactive
goto build_complete

:build_complete
if %errorlevel% neq 0 (
    echo ❌ Build failed! Check the error messages above.
    echo 💡 Common solutions:
    echo    • Check your internet connection
    echo    • Verify EAS account has sufficient build credits
    echo    • Ensure app.json configuration is correct
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo 🎉 BUILD COMPLETED SUCCESSFULLY!
echo ================================================================================
echo 📱 Your APK is ready for PlayStore submission!
echo.
echo 📋 Next steps:
echo    1. Download the APK from your EAS dashboard
echo    2. Test on a real Android device (not emulator)
echo    3. Create PlayStore listing
echo    4. Upload APK to Google Play Console
echo.
echo 🌐 View your builds: https://expo.dev/accounts/[username]/projects/financegpt2/builds
echo.
echo ⚡ Your API is running on: http://***************:9000
echo ✅ Users worldwide can now access your FinanceGPT!
echo ================================================================================

pause