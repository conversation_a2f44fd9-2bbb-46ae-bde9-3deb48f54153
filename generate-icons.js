const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Create a simple green icon with government/finance symbols
async function generateIcons() {
  const iconSizes = {
    icon: 1024,
    adaptiveIcon: 1024,
    favicon: 64,
    splash: 1024
  };

  // Create base SVG for the icon
  const createIconSVG = (size, isAdaptive = false) => {
    const center = size / 2;
    const scale = size / 1024; // Scale factor based on 1024px base
    
    return `
      <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#065f46"/>
            <stop offset="50%" stop-color="#059669"/>
            <stop offset="100%" stop-color="#10b981"/>
          </linearGradient>
          <linearGradient id="coinGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#fbbf24"/>
            <stop offset="100%" stop-color="#f59e0b"/>
          </linearGradient>
        </defs>
        
        <!-- Background -->
        <rect width="${size}" height="${size}" fill="url(#bgGrad)" rx="${isAdaptive ? size * 0.2 : 0}"/>
        
        <!-- Government building -->
        <g transform="translate(${center}, ${center})">
          <!-- Main building -->
          <rect x="${-60 * scale}" y="${-80 * scale}" width="${120 * scale}" height="${140 * scale}" fill="#1f2937" rx="${8 * scale}"/>
          
          <!-- Columns -->
          <rect x="${-50 * scale}" y="${-80 * scale}" width="${12 * scale}" height="${140 * scale}" fill="#374151" rx="${6 * scale}"/>
          <rect x="${-24 * scale}" y="${-80 * scale}" width="${12 * scale}" height="${140 * scale}" fill="#374151" rx="${6 * scale}"/>
          <rect x="${2 * scale}" y="${-80 * scale}" width="${12 * scale}" height="${140 * scale}" fill="#374151" rx="${6 * scale}"/>
          <rect x="${28 * scale}" y="${-80 * scale}" width="${12 * scale}" height="${140 * scale}" fill="#374151" rx="${6 * scale}"/>
          
          <!-- Roof -->
          <path d="M ${-80 * scale} ${-80 * scale} Q 0 ${-120 * scale} ${80 * scale} ${-80 * scale} L ${80 * scale} ${-70 * scale} Q 0 ${-110 * scale} ${-80 * scale} ${-70 * scale} Z" fill="#7c2d12"/>
          
          <!-- Star symbol -->
          <g transform="translate(0, ${-100 * scale})">
            <path d="M 0,${-20 * scale} L ${6 * scale},${-6 * scale} L ${20 * scale},${-6 * scale} L ${10 * scale},${2 * scale} L ${16 * scale},${16 * scale} L 0,${8 * scale} L ${-16 * scale},${16 * scale} L ${-10 * scale},${2 * scale} L ${-20 * scale},${-6 * scale} L ${-6 * scale},${-6 * scale} Z" fill="#dc2626"/>
          </g>
          
          <!-- Dollar signs around -->
          <text x="${-200 * scale}" y="${-200 * scale}" font-family="Arial Black" font-size="${80 * scale}" fill="rgba(255,255,255,0.8)" text-anchor="middle">$</text>
          <text x="${200 * scale}" y="${-200 * scale}" font-family="Arial Black" font-size="${80 * scale}" fill="rgba(255,255,255,0.8)" text-anchor="middle">$</text>
          <text x="${-200 * scale}" y="${200 * scale}" font-family="Arial Black" font-size="${80 * scale}" fill="rgba(255,255,255,0.8)" text-anchor="middle">$</text>
          <text x="${200 * scale}" y="${200 * scale}" font-family="Arial Black" font-size="${80 * scale}" fill="rgba(255,255,255,0.8)" text-anchor="middle">$</text>
        </g>
      </svg>
    `;
  };

  try {
    console.log('🎨 Generating app icons...');
    
    // Generate main app icon
    const iconSVG = createIconSVG(iconSizes.icon);
    await sharp(Buffer.from(iconSVG))
      .png()
      .toFile('./assets/icon.png');
    console.log('✅ Main icon generated: ./assets/icon.png');
    
    // Generate adaptive icon (with rounded corners for Android)
    const adaptiveIconSVG = createIconSVG(iconSizes.adaptiveIcon, true);
    await sharp(Buffer.from(adaptiveIconSVG))
      .png()
      .toFile('./assets/adaptive-icon.png');
    console.log('✅ Adaptive icon generated: ./assets/adaptive-icon.png');
    
    // Generate favicon
    const faviconSVG = createIconSVG(iconSizes.favicon);
    await sharp(Buffer.from(faviconSVG))
      .png()
      .toFile('./assets/favicon.png');
    console.log('✅ Favicon generated: ./assets/favicon.png');
    
    // Generate splash screen
    const splashSVG = `
      <svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="splashGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#065f46"/>
            <stop offset="50%" stop-color="#059669"/>
            <stop offset="100%" stop-color="#10b981"/>
          </linearGradient>
        </defs>
        <rect width="1024" height="1024" fill="url(#splashGrad)"/>
        <g transform="translate(512, 512)">
          <circle cx="0" cy="0" r="200" fill="rgba(255,255,255,0.1)"/>
          <text x="0" y="20" font-family="Arial Black" font-size="120" fill="white" text-anchor="middle">💰</text>
          <text x="0" y="180" font-family="Arial" font-size="48" fill="white" text-anchor="middle" opacity="0.9">Finance GPT</text>
        </g>
      </svg>
    `;
    await sharp(Buffer.from(splashSVG))
      .png()
      .toFile('./assets/splash-icon.png');
    console.log('✅ Splash icon generated: ./assets/splash-icon.png');
    
    console.log('🎉 All icons generated successfully!');
    
  } catch (error) {
    console.error('❌ Error generating icons:', error);
    process.exit(1);
  }
}

// Run the generator
generateIcons();