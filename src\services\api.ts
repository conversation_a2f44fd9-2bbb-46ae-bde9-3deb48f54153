import axios, { AxiosInstance, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

import { Config } from '../constants/Config';
import { ChatMessage, ChatResponse, DownloadResponse, HealthResponse, APIError } from '../types/api';

class ApiService {
  private client: AxiosInstance;
  private baseURL: string;
  private currentSessionId: string | null = null;

  constructor() {
    // You can change this to your actual backend URL
    this.baseURL = Config.API_BASE_URL;

    // Add comprehensive logging for debugging
    console.log('🚀 ApiService Constructor:');
    console.log(`   Base URL: ${this.baseURL}`);
    console.log(`   Timeout: ${Config.REQUEST_TIMEOUT}ms`);
    console.log(`   User Agent: ${Config.USER_AGENT}`);

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: Config.REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': Config.USER_AGENT,
      },
    });

    this.setupInterceptors();
    this.loadSessionFromStorage();
  }

  private setupInterceptors() {
    // Request interceptor to add session ID
    this.client.interceptors.request.use(
      async (config) => {
        // Add session ID to requests if available
        if (this.currentSessionId && config.data) {
          config.data.session_id = this.currentSessionId;
        }

        // Ensure headers object exists for all requests
        if (!config.headers) {
          config.headers = config.headers || {};
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Save session ID from response
        if (response.data?.session_id) {
          this.saveSessionId(response.data.session_id);
        }
        return response;
      },
      async (error: unknown) => {
        // Handle API errors with advanced error formatting
        if (axios.isAxiosError(error)) {
          if (error.response?.status === 401) {
            // Clear invalid session
            await this.clearSession();
          }

          // Handle API errors
          if (error.response?.status === 503 &&
              typeof error.response?.data === 'string' &&
              error.response.data.includes('ERR_NGROK_3004')) {
            console.error('🚨 Ngrok gateway error detected - tunnel may be down');
          }
        }

        return Promise.reject(this.formatError(error));
      }
    );
  }

  private async loadSessionFromStorage() {
    try {
      console.log('📂 Loading session from storage...');
      const sessionId = await AsyncStorage.getItem(Config.SESSION_STORAGE_KEY);
      if (sessionId) {
        this.currentSessionId = sessionId;
        console.log('✅ Session loaded from storage:', sessionId);
      } else {
        console.log('📭 No session found in storage');
      }
    } catch (error) {
      console.warn('Failed to load session from storage:', error);
    }
  }

  private async saveSessionId(sessionId: string) {
    this.currentSessionId = sessionId;
    try {
      await AsyncStorage.setItem(Config.SESSION_STORAGE_KEY, sessionId);
    } catch (error) {
      console.warn('Failed to save session to storage:', error);
    }
  }

  private async clearSession() {
    this.currentSessionId = null;
    try {
      await AsyncStorage.removeItem(Config.SESSION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear session from storage:', error);
    }
  }

  private formatError(error: unknown): APIError {
    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      // Handle 522 Cloudflare timeout specifically
      if (error.response?.status === 522 ||
          (error.response?.data && typeof error.response.data === 'string' &&
           error.response.data.includes('522'))) {
        return {
          error: 'Server Timeout',
          message: 'The server is taking too long to respond. It might be processing your request. Please wait a moment and try again.',
          code: 522,
          details: error.response?.data,
        };
      }

      if (error.response?.data) {
        return {
          error: error.response.data.error || 'API Error',
          message: error.response.data.message || 'An error occurred',
          code: error.response.status,
          details: error.response.data,
        };
      }

      if (error.code === 'ECONNABORTED') {
        return {
          error: 'Timeout',
          message: 'Request timed out after 15 seconds. The server might be busy. Please try again.',
          code: 408,
        };
      }

      if (error.code === 'ERR_NETWORK') {
        return {
          error: 'Network Error',
          message: 'Cannot reach the server. Please check your internet connection.',
          code: 0,
        };
      }

      return {
        error: 'Request Error',
        message: error.message || 'An error occurred during the request',
        code: error.response?.status || 500,
      };
    }

    // Handle generic errors
    if (error instanceof Error) {
      return {
        error: 'Application Error',
        message: error.message,
        code: 500,
      };
    }

    // Handle unknown errors
    return {
      error: 'Unknown Error',
      message: 'An unexpected error occurred',
      code: 500,
    };
  }

  // Check network connectivity before making requests
  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isConnected === true && state.isInternetReachable !== false;
    } catch (error) {
      console.warn('Failed to check network connectivity:', error);
      return true; // Assume connected if check fails
    }
  }

  // Retry logic with exponential backoff
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = Config.MAX_RETRIES,
    retryDelay: number = Config.RETRY_DELAY
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Check network before each attempt
        const isConnected = await this.checkNetworkConnectivity();
        if (!isConnected) {
          throw new Error('No internet connection');
        }

        // Try the request
        return await requestFn();
      } catch (error) {
        lastError = error;

        // Don't retry on certain errors
        if (axios.isAxiosError(error)) {
          const status = error.response?.status;
          // Don't retry on client errors (4xx) except 408 (timeout), 429 (rate limit)
          if (status && status >= 400 && status < 500 && status !== 408 && status !== 429) {
            throw error;
          }
        }

        // If this was the last attempt, throw the error
        if (attempt === maxRetries) {
          throw error;
        }

        // Calculate delay with exponential backoff
        const delay = retryDelay * Math.pow(2, attempt);
        console.log(`🔄 Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms...`);

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  async setSessionId(sessionId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(Config.SESSION_STORAGE_KEY, sessionId);
    } catch (error) {
      console.error('Error setting session ID:', error);
    }
  }

  async createNewSession(): Promise<string> {
    console.log('🚀 Creating new mobile session...');
    
    try {
      // Generate a unique mobile session ID first
      const newSessionId = this.generateMobileSessionId();
      console.log('🆕 Generated new mobile session ID:', newSessionId);
      
      // Save it immediately to prevent race conditions
      this.currentSessionId = newSessionId;
      await this.saveSessionId(newSessionId);
      
      // Try to validate it with the backend
      try {
        const validationResult = await this.validateSession(newSessionId);
        if (validationResult.valid && validationResult.session_id) {
          console.log('✅ Session validated by backend:', validationResult.session_id);
          this.currentSessionId = validationResult.session_id;
          await this.saveSessionId(validationResult.session_id);
          return validationResult.session_id;
        }
      } catch (validationError) {
        console.warn('⚠️ Session validation failed, using generated ID:', validationError);
      }
      
      console.log('✅ Using generated mobile session ID:', newSessionId);
      return newSessionId;
      
    } catch (error) {
      console.error('❌ Failed to create new session:', error);
      throw new Error('Failed to create new session');
    }
  }

  async sendMessage(message: string): Promise<ChatResponse> {
    // Wrap the entire request in retry logic
    return this.retryRequest(async () => {
      try {
        console.log('💬 sendMessage called with:', message);
        console.log('📍 Current API Base URL:', this.baseURL);

        // Ensure we have a valid session ID
        if (!this.currentSessionId) {
          console.log('🔄 No session found, creating new mobile session...');
          await this.createNewSession();
        }

        // Double-check session ID is available
        if (!this.currentSessionId) {
          throw new Error('Failed to create or load session ID');
        }

        const payload: ChatMessage = {
          message: message.trim(),
          channel: 'mobile',  // This will trigger mobile session management
          session_id: this.currentSessionId,
          device_info: {
            platform: 'react-native',
            version: Config.APP_VERSION,
            app_version: Config.APP_VERSION,
          },
        };

        console.log('📤 Sending message to unified chat endpoint:');
        console.log('   URL:', `${this.baseURL}${Config.ENDPOINTS.CHAT}`);
        console.log('   Payload:', JSON.stringify(payload, null, 2));

        const response = await this.client.post(Config.ENDPOINTS.CHAT, payload);
        console.log('📥 Unified chat response:', response.data);

        // Extract download information from response (like WhatsApp/WordPress)
        if (response.data.downloadable_data) {
          console.log('📎 Downloadable data available:', response.data.downloadable_data);
        }

        return response.data;
      } catch (error) {
        console.error('❌ Error sending message:');
        console.error('   Message:', message);
        console.error('   API URL:', `${this.baseURL}${Config.ENDPOINTS.CHAT}`);
        console.error('   Error details:', error);

        if (axios.isAxiosError(error)) {
          if (error.response) {
            console.error('   Response status:', error.response.status);
            console.error('   Response data:', error.response.data);
          } else if (error.request) {
            console.error('   No response received');
            console.error('   Request details:', error.request);
          } else {
            console.error('   Error setting up request:', error.message);
          }
        } else if (error instanceof Error) {
          console.error('   Error message:', error.message);
        }

        throw error;
      }
    });
  }

  async downloadFile(format: 'excel' | 'pdf' | 'csv', type?: string): Promise<DownloadResponse> {
    try {
      if (!this.currentSessionId) {
        throw new Error('No active session. Please send a message first.');
      }

      // Use unified download endpoint (like WhatsApp/WordPress)
      const url = `/download/${this.currentSessionId}`;
      const params = { format, ...(type && { type }) };

      console.log('📥 Downloading file from unified endpoint:', { format, type, sessionId: this.currentSessionId });
      const response = await this.client.get(url, { params });

      return {
        download_url: response.data.download_url || `${this.baseURL}${url}?${new URLSearchParams(params)}`,
        filename: response.data.filename || `financegpt_${this.currentSessionId}.${format}`,
        file_size: response.data.file_size,
        expires_at: response.data.expires_at,
      };
    } catch (error) {
      console.error('Error downloading file:', error);
      throw this.formatError(error);
    }
  }

  async uploadPDF(fileUri: string): Promise<ChatResponse> {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: fileUri,
        type: 'application/pdf',
        name: 'document.pdf',
      } as any);

      const response = await this.client.post('/pdf/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Failed to upload PDF');
      }
    } catch (error) {
      console.error('Error uploading PDF:', error);
      throw this.formatError(error);
    }
  }

  async healthCheck(): Promise<HealthResponse> {
    try {
      // Health check with shorter timeout and no retries
      const response = await this.client.get(Config.ENDPOINTS.HEALTH, {
        timeout: 5000, // 5 second timeout for health check
      });
      return response.data;
    } catch (error) {
      console.error('Health check failed:', error);
      throw this.formatError(error);
    }
  }

  // Quick connectivity check (doesn't retry)
  async isServerReachable(): Promise<boolean> {
    try {
      const isConnected = await this.checkNetworkConnectivity();
      if (!isConnected) {
        return false;
      }

      await this.client.get(Config.ENDPOINTS.HEALTH, {
        timeout: 3000, // 3 second timeout
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get current session ID (synchronous)
  getSessionId(): string | null {
    return this.currentSessionId;
  }

  // Get current session ID (asynchronous - waits for storage to load)
  async getSessionIdAsync(): Promise<string | null> {
    // If session is already loaded, return it immediately
    if (this.currentSessionId) {
      return this.currentSessionId;
    }
    
    // Otherwise, wait for storage to load
    await this.loadSessionFromStorage();
    return this.currentSessionId;
  }

  // Clear current session and create a new one
  async resetSession(): Promise<string> {
    console.log('🔄 Resetting session...');
    
    // Clear current session
    this.currentSessionId = null;
    await AsyncStorage.removeItem(Config.SESSION_STORAGE_KEY);
    
    // Create a new session
    const newSessionId = await this.createNewSession();
    console.log('✅ Session reset complete:', newSessionId);
    return newSessionId;
  }

  // Generate a unique mobile session ID
  private generateMobileSessionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `mobile_${timestamp}_${random}`;
  }

  // Create new session by sending initial message to chat endpoint
  async createSession(): Promise<{ session_id: string; status: string }> {
    try {
      console.log('🚀 Creating new mobile session via chat endpoint...');
      
      // Generate a unique mobile session ID
      const newSessionId = this.generateMobileSessionId();
      console.log('🆕 Generated new mobile session ID:', newSessionId);
      
      // Send a simple greeting to establish session with our generated ID
      const payload = {
        message: "Hello", // Simple greeting to establish session
        channel: 'mobile',
        session_id: newSessionId, // Use our generated session ID
        device_info: {
          platform: Platform.OS,
          version: Config.APP_VERSION,
          app_version: Config.APP_VERSION,
        },
      };
      
      console.log('📤 Session creation payload:', payload);
      const response = await this.client.post(Config.ENDPOINTS.CHAT, payload);
      console.log('📥 Session creation response:', response.data);
      
      // Check if backend accepted our session ID or provided a different one
      let finalSessionId = newSessionId;
      if (response.data && response.data.session_info && response.data.session_info.session_id) {
        finalSessionId = response.data.session_info.session_id;
        console.log('📋 Backend provided session ID:', finalSessionId);
      } else if (response.data && response.data.session_id) {
        finalSessionId = response.data.session_id;
        console.log('📋 Backend provided session ID:', finalSessionId);
      }
      
      // Save the final session ID
      this.currentSessionId = finalSessionId;
      await this.saveSessionId(finalSessionId);
      console.log('✅ Mobile session created successfully:', finalSessionId);
      return { session_id: finalSessionId, status: 'created' };
      
    } catch (error) {
      console.error('❌ Session creation failed:', error);
      throw this.formatError(error);
    }
  }

  // Validate existing session by sending a simple message
  async validateSession(sessionId: string): Promise<{ valid: boolean; session_id?: string }> {
    try {
      const response = await this.client.post(Config.ENDPOINTS.CHAT, {
        message: "validate_session", // Simple validation message
        channel: 'mobile',
        session_id: sessionId,
        device_info: {
          platform: Platform.OS,
          version: Config.APP_VERSION,
          app_version: Config.APP_VERSION,
        },
      });
      
      // If we get a valid response, the session is valid
      if (response.data && response.data.session_info && response.data.session_info.session_id) {
        return { valid: true, session_id: response.data.session_info.session_id };
      } else if (response.data && response.data.session_id) {
        return { valid: true, session_id: response.data.session_id };
      } else {
        return { valid: false };
      }
    } catch (error) {
      console.warn('⚠️ Session validation failed:', error);
      return { valid: false };
    }
  }
}

export const apiService = new ApiService();