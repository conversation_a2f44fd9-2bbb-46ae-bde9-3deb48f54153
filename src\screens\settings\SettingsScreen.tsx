/**
 * SettingsScreen - App Settings with Theme Toggle
 */

import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { Typography, Spacing, BorderRadius, Shadows } from '../../theme';

interface SettingsScreenProps {
  onClose: () => void;
}

export const SettingsScreen = memo<SettingsScreenProps>(({ onClose }) => {
  const { theme, themeMode, setThemeMode, isDark } = useTheme();

  const themeOptions = [
    { value: 'light' as const, label: 'Light', icon: 'sunny' as const },
    { value: 'dark' as const, label: 'Dark', icon: 'moon' as const },
    { value: 'auto' as const, label: 'Auto', icon: 'phone-portrait' as const },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]} edges={['top']}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.surface, borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.textPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.textPrimary }]}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Appearance Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.textSecondary }]}>APPEARANCE</Text>

          {/* Theme Mode Options */}
          <View style={[styles.card, { backgroundColor: theme.surface }]}>
            {themeOptions.map((option, index) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.themeOption,
                  index !== themeOptions.length - 1 && styles.themeOptionBorder,
                  { borderBottomColor: theme.border },
                ]}
                onPress={() => setThemeMode(option.value)}
                activeOpacity={0.7}
              >
                <View style={styles.themeOptionLeft}>
                  <View
                    style={[
                      styles.themeIconContainer,
                      {
                        backgroundColor:
                          themeMode === option.value ? theme.primary : theme.inputBg,
                      },
                    ]}
                  >
                    <Ionicons
                      name={option.icon}
                      size={20}
                      color={themeMode === option.value ? theme.textOnPrimary : theme.textSecondary}
                    />
                  </View>
                  <View style={styles.themeOptionText}>
                    <Text style={[styles.themeOptionLabel, { color: theme.textPrimary }]}>
                      {option.label}
                    </Text>
                    {option.value === 'auto' && (
                      <Text style={[styles.themeOptionDescription, { color: theme.textSecondary }]}>
                        Match system theme
                      </Text>
                    )}
                  </View>
                </View>
                {themeMode === option.value && (
                  <Ionicons name="checkmark-circle" size={24} color={theme.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Theme Preview */}
          <View style={[styles.previewCard, { backgroundColor: theme.surface }]}>
            <View style={styles.previewHeader}>
              <Ionicons name="eye-outline" size={20} color={theme.textSecondary} />
              <Text style={[styles.previewTitle, { color: theme.textPrimary }]}>
                Current Theme: {isDark ? 'Dark' : 'Light'}
              </Text>
            </View>
            <View style={styles.previewContent}>
              {/* Sample Message Bubbles */}
              <View style={[styles.previewBubbleUser, { backgroundColor: theme.primary }]}>
                <Text style={[styles.previewBubbleText, { color: theme.textOnPrimary }]}>
                  User message
                </Text>
              </View>
              <View style={[styles.previewBubbleBot, { backgroundColor: theme.botMessageBg }]}>
                <Text style={[styles.previewBubbleText, { color: theme.textPrimary }]}>
                  Bot response
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* About Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.textSecondary }]}>ABOUT</Text>
          <View style={[styles.card, { backgroundColor: theme.surface }]}>
            <View style={styles.aboutRow}>
              <Text style={[styles.aboutLabel, { color: theme.textSecondary }]}>Version</Text>
              <Text style={[styles.aboutValue, { color: theme.textPrimary }]}>5.0.0</Text>
            </View>
            <View style={[styles.aboutRow, styles.aboutRowBorder, { borderTopColor: theme.border }]}>
              <Text style={[styles.aboutLabel, { color: theme.textSecondary }]}>Organization</Text>
              <Text style={[styles.aboutValue, { color: theme.textPrimary }]}>
                Govt. of Balochistan
              </Text>
            </View>
          </View>
        </View>

        {/* Info */}
        <View style={styles.infoContainer}>
          <Ionicons name="information-circle-outline" size={16} color={theme.textMuted} />
          <Text style={[styles.infoText, { color: theme.textMuted }]}>
            Theme changes apply immediately
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
});

SettingsScreen.displayName = 'SettingsScreen';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingTop: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    letterSpacing: 1,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  card: {
    marginHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.lg,
  },
  themeOptionBorder: {
    borderBottomWidth: 1,
  },
  themeOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  themeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  themeOptionText: {
    flex: 1,
  },
  themeOptionLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  themeOptionDescription: {
    fontSize: Typography.fontSize.sm,
    marginTop: 2,
  },
  previewCard: {
    marginHorizontal: Spacing.lg,
    marginTop: Spacing.lg,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  previewTitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginLeft: Spacing.sm,
  },
  previewContent: {
    gap: Spacing.sm,
  },
  previewBubbleUser: {
    alignSelf: 'flex-end',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    maxWidth: '70%',
  },
  previewBubbleBot: {
    alignSelf: 'flex-start',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    maxWidth: '70%',
  },
  previewBubbleText: {
    fontSize: Typography.fontSize.sm,
  },
  aboutRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.lg,
  },
  aboutRowBorder: {
    borderTopWidth: 1,
  },
  aboutLabel: {
    fontSize: Typography.fontSize.base,
  },
  aboutValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
    gap: Spacing.xs,
  },
  infoText: {
    fontSize: Typography.fontSize.xs,
  },
});

