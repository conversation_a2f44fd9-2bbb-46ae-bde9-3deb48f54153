/**
 * ChatInterface - Modern ChatGPT-inspired UI
 * With sidebar menu, separated input controls, and responsive design
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
  FlatList,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { IMessage, User } from 'react-native-gifted-chat';
import { StatusBar } from 'expo-status-bar';
import { apiService } from '../../services/api';
import { unifiedLogger } from '../../services/unifiedLogger';
import {
  ChatHeader,
  SidebarDrawer,
  ChatInputBar,
  ModernMessageBubble,
  AnimatedMessageBubble,
  ModernTypingIndicator,
} from '../../components/modern';
import { useTheme } from '../../contexts/ThemeContext';
import { SettingsScreen } from '../settings';
import * as DocumentPicker from 'expo-document-picker';
import { useFileAttachmentHandler } from './components/fileAttachmentHandler/FileAttachmentHandler';
import { useQuickReplyHandler } from './components/quickReplyHandler/QuickReplyHandler';
import { GiftedChat } from 'react-native-gifted-chat';

const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

export default function ChatInterface() {
  const { theme, isDark } = useTheme();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [inputText, setInputText] = useState('');
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [animatedMessages, setAnimatedMessages] = useState<Set<string>>(new Set());

  // User configuration
  const user: User = useMemo(() => ({
    _id: 1,
    name: 'You',
    avatar: '👤',
  }), []);

  // Bot configuration
  const botUser: User = useMemo(() => ({
    _id: 2,
    name: 'FinanceGPT',
    avatar: CHAT_AVATAR,
  }), []);

  // Initialize chat
  useEffect(() => {
    initializeChat();
  }, []);

  const initializeChat = useCallback(async () => {
    unifiedLogger.logComponentLifecycle('ChatInterface', 'initialize_start');

    try {
      let currentSessionId = await apiService.getSessionIdAsync();

      if (currentSessionId === 'unknown' || !currentSessionId) {
        const newSessionId = await apiService.resetSession();
        setSessionId(newSessionId);
      } else {
        setSessionId(currentSessionId);
      }

      const welcomeMessage: IMessage = {
        _id: Math.random().toString(),
        text: 'Welcome to FinanceGPT! 🏛️\n\nI can help you with:\n• 👥 Employee records\n• 💰 Budget data\n• 🏗️ PSDP projects\n• 📄 Document analysis\n\nWhat would you like to know?',
        createdAt: new Date(),
        user: botUser,
      };

      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Error initializing chat:', error);
    }

    unifiedLogger.logComponentLifecycle('ChatInterface', 'initialize_complete');
  }, [botUser]);

  // Snackbar helper
  const showSnackbar = useCallback((message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
    setTimeout(() => setSnackbarVisible(false), 3000);
  }, []);

  // Handle new chat
  const handleNewChat = useCallback(async () => {
    Alert.alert(
      'New Chat',
      'Start a new conversation?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'New Chat',
          onPress: async () => {
            await apiService.resetSession();
            initializeChat();
          },
        },
      ]
    );
  }, [initializeChat]);

  // File attachment handler
  const fileAttachmentHandler = useFileAttachmentHandler({
    onFileAttachment: (message: IMessage) => {
      setMessages((prev) => [message, ...prev]);
    },
    showSnackbar,
  });

  // Quick reply handler
  const quickReplyHandler = useQuickReplyHandler({
    onSend: async (messages: IMessage[]) => {
      setMessages((prev) => GiftedChat.append(prev, messages));
    },
    showSnackbar,
    setHasDownloadableData: () => {},
    setCurrentDownloadData: () => {},
    onNewSession: handleNewChat,
    handleFileOperation: fileAttachmentHandler.handleFileOperation,
    isProcessingFile,
    setIsProcessingFile,
  });

  // Send message
  const handleSend = useCallback(async () => {
    const text = inputText.trim();
    if (!text) return;

    const userMessage: IMessage = {
      _id: Math.random().toString(),
      text,
      createdAt: new Date(),
      user,
    };

    setMessages((prev) => [userMessage, ...prev]);
    setInputText('');
    setIsTyping(true);

    try {
      const response = await apiService.sendMessage(text);

      if (response.success && response.response) {
        const botMessageId = Math.random().toString();
        const botMessage: IMessage = {
          _id: botMessageId,
          text: response.response,
          createdAt: new Date(),
          user: botUser,
        };

        // Mark this message for animation
        setAnimatedMessages(prev => new Set([...prev, botMessageId]));
        setMessages((prev) => [botMessage, ...prev]);

        // Check if there's a file to download
        const hasDownload = !!(response.download_url || response.file_path || response.download_ready);
        if (hasDownload && (response.file_path || response.download_url)) {
          const currentSessionId = apiService.getSessionId();
          if (currentSessionId) {
            const fileFormat = response.file_type || (response.filename && response.filename.split('.').pop()) || 'excel';
            const filePath = response.file_path || response.download_url;
            const fileName = response.filename || `financegpt_data_${currentSessionId}.${fileFormat}`;
            if (filePath) {
              setTimeout(() => {
                fileAttachmentHandler.createFileAttachment(filePath, fileName, fileFormat, currentSessionId);
              }, 1500);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsTyping(false);
    }
  }, [inputText, user, botUser, fileAttachmentHandler]);

  // Handle attachment
  const handleAttachment = useCallback(async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        Alert.alert('File Selected', `${file.name}\nSize: ${(file.size || 0) / 1024} KB`);
        // TODO: Implement file upload
      }
    } catch (error) {
      console.error('Error picking document:', error);
    }
  }, []);

  // Handle voice
  const handleVoice = useCallback(() => {
    setIsRecording((prev) => !prev);
    Alert.alert(
      isRecording ? 'Stop Recording' : 'Start Recording',
      'Voice recording feature coming soon!'
    );
  }, [isRecording]);

  // Sidebar menu items
  const menuItems = useMemo(() => [
    {
      id: 'new-chat',
      title: 'New Chat',
      icon: 'add-circle-outline' as const,
      onPress: handleNewChat,
    },
    {
      id: 'history',
      title: 'Chat History',
      icon: 'time-outline' as const,
      onPress: () => Alert.alert('Chat History', 'Coming soon!'),
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings-outline' as const,
      onPress: () => setSettingsVisible(true),
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline' as const,
      onPress: () => Alert.alert('Help', 'Contact <EMAIL>'),
    },
    {
      id: 'about',
      title: 'About',
      icon: 'information-circle-outline' as const,
      onPress: () => Alert.alert('About', 'FinanceGPT v2.0.0\nGovernment of Balochistan'),
    },
  ], [handleNewChat]);

  // Handle animation completion
  const handleAnimationComplete = useCallback((messageId: string) => {
    setAnimatedMessages(prev => {
      const newSet = new Set(prev);
      newSet.delete(messageId);
      return newSet;
    });
  }, []);

  // Render message
  const renderMessage = useCallback(({ item }: { item: IMessage }) => {
    const isUser = item.user._id === user._id;
    const hasQuickReplies = item.quickReplies && item.quickReplies.values && item.quickReplies.values.length > 0;
    const shouldAnimate = !isUser && animatedMessages.has(item._id.toString());

    return (
      <View>
        <AnimatedMessageBubble
          message={item}
          isUser={isUser}
          showAvatar={!isUser}
          enableAnimation={shouldAnimate}
          animationSpeed={25} // Adjust speed as needed
          onAnimationComplete={() => handleAnimationComplete(item._id.toString())}
        />
        {hasQuickReplies && !isUser && (
          <View style={styles.quickRepliesContainer}>
            {item.quickReplies!.values.map((reply, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.quickReplyButton, { backgroundColor: theme.surface, borderColor: theme.border }]}
                onPress={() => quickReplyHandler.handleQuickReply(reply)}
                activeOpacity={0.7}
              >
                <Text style={[styles.quickReplyText, { color: theme.textPrimary }]}>
                  {reply.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  }, [user._id, theme, quickReplyHandler, animatedMessages, handleAnimationComplete]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]} edges={['top']}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Header */}
      <ChatHeader
        onMenuPress={() => setSidebarVisible(true)}
        onNewChat={handleNewChat}
      />

      {/* Messages */}
      <KeyboardAvoidingView
        style={styles.flex}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item._id.toString()}
          inverted
          contentContainerStyle={styles.messageList}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          ListHeaderComponent={
            isTyping ? <ModernTypingIndicator showAvatar /> : null
          }
        />

        {/* Input Bar */}
        <ChatInputBar
          value={inputText}
          onChangeText={setInputText}
          onSend={handleSend}
          onAttachment={handleAttachment}
          onVoice={handleVoice}
          disabled={isTyping}
          isRecording={isRecording}
        />
      </KeyboardAvoidingView>

      {/* Sidebar */}
      <SidebarDrawer
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
        menuItems={menuItems}
        userName="Government User"
        userEmail="<EMAIL>"
      />

      {/* Settings Modal */}
      <Modal
        visible={settingsVisible}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <SettingsScreen onClose={() => setSettingsVisible(false)} />
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flex: {
    flex: 1,
  },
  messageList: {
    paddingVertical: 8,
  },
  quickRepliesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  quickReplyButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  quickReplyText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

