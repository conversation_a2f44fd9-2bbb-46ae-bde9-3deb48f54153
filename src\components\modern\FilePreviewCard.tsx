/**
 * FilePreviewCard Component
 * Modern file preview card with download and share buttons
 * Optimized for both iOS and Android
 */

import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../../theme';

interface FilePreviewCardProps {
  fileName: string;
  fileType: 'xlsx' | 'pdf' | 'csv';
  fileSize?: string;
  onDownload: () => void;
  onShare: () => void;
}

const getFileIcon = (fileType: string): keyof typeof Ionicons.glyphMap => {
  switch (fileType.toLowerCase()) {
    case 'xlsx':
    case 'xls':
      return 'document-text';
    case 'pdf':
      return 'document';
    case 'csv':
      return 'list';
    default:
      return 'document';
  }
};

const getFileTypeLabel = (fileType: string): string => {
  switch (fileType.toLowerCase()) {
    case 'xlsx':
    case 'xls':
      return 'Excel Spreadsheet';
    case 'pdf':
      return 'PDF Document';
    case 'csv':
      return 'CSV File';
    default:
      return 'File';
  }
};

export const FilePreviewCard = memo<FilePreviewCardProps>(
  ({ fileName, fileType, fileSize, onDownload, onShare }) => {
    const handleDownload = () => {
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      onDownload();
    };

    const handleShare = () => {
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      onShare();
    };

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name={getFileIcon(fileType)} size={32} color={Colors.accent} />
          </View>
          <View style={styles.fileInfo}>
            <Text style={styles.fileName} numberOfLines={1}>
              {fileName}
            </Text>
            <Text style={styles.fileDetails}>
              {fileSize ? `${fileSize} • ` : ''}
              {getFileTypeLabel(fileType)}
            </Text>
          </View>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.button}
            onPress={handleDownload}
            activeOpacity={0.7}
          >
            <Ionicons name="download-outline" size={18} color={Colors.textPrimary} />
            <Text style={styles.buttonText}>Download</Text>
          </TouchableOpacity>

          <View style={styles.buttonSpacer} />

          <TouchableOpacity
            style={styles.button}
            onPress={handleShare}
            activeOpacity={0.7}
          >
            <Ionicons name="share-outline" size={18} color={Colors.textPrimary} />
            <Text style={styles.buttonText}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
);

FilePreviewCard.displayName = 'FilePreviewCard';

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.lg,
    marginTop: Spacing.sm,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
        shadowColor: Colors.shadowLight,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  fileDetails: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textSecondary,
  },
  actions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.sm + 2,
    paddingHorizontal: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.xs,
  },
  buttonSpacer: {
    width: Spacing.sm,
  },
  buttonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.textPrimary,
  },
});

