/**
 * ModernMessageBubble Component
 * Clean, modern message bubble with simple grayish backgrounds
 * Optimized for both iOS and Android
 */

import React, { memo } from 'react';
import { View, Text, StyleSheet, Image, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Typography, Spacing, BorderRadius } from '../../theme';

const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

interface ModernMessageBubbleProps {
  message: {
    _id: string | number;
    text: string;
    createdAt: Date | number;
    user: {
      _id: string | number;
      name?: string;
      avatar?: string;
    };
  };
  isUser: boolean;
  showAvatar?: boolean;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
}

export const ModernMessageBubble = memo<ModernMessageBubbleProps>(
  ({ message, isUser, showAvatar = true, isFirstInGroup = true, isLastInGroup = true }) => {
    const { theme, isDark } = useTheme();

    if (isUser) {
      return (
        <View style={styles.userContainer}>
          <View
            style={[
              styles.userBubble,
              {
                backgroundColor: isDark ? '#2F7C6E' : '#10A37F',
                borderBottomRightRadius: isLastInGroup ? BorderRadius.sm : BorderRadius.xl,
              },
            ]}
          >
            <Text style={styles.userText}>{message.text}</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.botContainer}>
        {showAvatar && isLastInGroup ? (
          <View style={styles.avatarContainer}>
            <Image source={CHAT_AVATAR} style={styles.avatar} resizeMode="cover" />
          </View>
        ) : (
          <View style={styles.avatarPlaceholder} />
        )}
        <View
          style={[
            styles.botBubble,
            {
              backgroundColor: isDark ? '#444654' : '#F7F7F8',
              borderBottomLeftRadius: isLastInGroup ? BorderRadius.sm : BorderRadius.xl,
            },
          ]}
        >
          <Text style={[styles.botText, { color: theme.textPrimary }]}>{message.text}</Text>
        </View>
      </View>
    );
  }
);

ModernMessageBubble.displayName = 'ModernMessageBubble';

const styles = StyleSheet.create({
  // User message styles
  userContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xs,
    maxWidth: '100%',
  },
  userBubble: {
    maxWidth: '80%',
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  userText: {
    color: '#FFFFFF',
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * 1.5,
    fontWeight: '400',
  },

  // Bot message styles
  botContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xs,
    maxWidth: '100%',
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: Spacing.sm,
    overflow: 'hidden',
    backgroundColor: '#E5E5E5',
  },
  avatar: {
    width: 32,
    height: 32,
  },
  avatarPlaceholder: {
    width: 32,
    marginRight: Spacing.sm,
  },
  botBubble: {
    maxWidth: '85%',
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  botText: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * 1.5,
    fontWeight: '400',
  },
});

