import React, { useState, useEffect, useRef } from 'react';
import { PaperProvider, MD3DarkTheme, MD3LightTheme } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Animated, StatusBar } from 'react-native';
import { AppNavigator } from './src/navigation';
import IntroScreen from './src/screens/introScreen';
import { Colors } from './src/constants/Colors';
import { ThemeProvider } from './src/contexts/ThemeContext';
import 'react-native-gesture-handler';

const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: Colors.primary,
    secondary: Colors.secondary,
    background: Colors.background,
    surface: Colors.surface,
    onPrimary: Colors.textOnPrimary,
    onSecondary: Colors.textOnSecondary,
    onSurface: Colors.text,
  },
};

export default function App() {
  const [showIntro, setShowIntro] = useState(true);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleAnimationComplete = () => {
    // Fade out the intro screen
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      setShowIntro(false);
    });
  };

  if (showIntro) {
    return (
      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        <IntroScreen onAnimationComplete={handleAnimationComplete} />
      </Animated.View>
    );
  }

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <PaperProvider theme={theme}>
          <AppNavigator />
        </PaperProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
