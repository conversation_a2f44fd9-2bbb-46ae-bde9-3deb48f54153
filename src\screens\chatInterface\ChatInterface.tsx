import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  Image,
  Dimensions,
  Modal,
  TextInput,
  ScrollView,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GiftedChat, IMessage, User } from 'react-native-gifted-chat';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../../constants/Colors';
import { apiService } from '../../services/api';
import { unifiedLogger } from '../../services/unifiedLogger';
import { ModernChatTheme } from '../../components/financeGPTTheme';
import {
  useFileAttachmentHandler,
  useQuickReplyHandler,
  ChatSnackbar,
  TypingIndicator,
} from './components';
// Modern components
import {
  ModernMessageBubble,
  AnimatedMessageBubble,
  ModernInputBar,
  ModernTypingIndicator,
  FilePreviewCard,
} from '../../components/modern';
import { Colors as ThemeColors } from '../../theme';

interface Props {
  onNewSession?: () => void;
}

// Avatar image loaded once outside component to prevent re-renders
const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

export default function ChatInterface({ onNewSession }: Props) {
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [keyboardHeight] = useState(0);
  const [hasDownloadableData, setHasDownloadableData] = useState(false);
  const [currentDownloadData, setCurrentDownloadData] = useState<any>(null);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [inputText, setInputText] = useState('');
  const [animatedMessages, setAnimatedMessages] = useState<Set<string>>(new Set());








  // User configuration - memoized to prevent re-renders
  const user: User = useMemo(() => ({
    _id: 1,
    name: 'You',
    avatar: '👤',
  }), []);

  // FinanceGPT Bot configuration - memoized to prevent re-renders
  const botUser: User = useMemo(() => ({
    _id: 2,
    name: 'FinanceGPT',
    avatar: CHAT_AVATAR,
  }), []);

  useEffect(() => {
    // Delay initialization to prevent white notification on startup
    const timer = setTimeout(() => {
      initializeChat();
      setIsInitialized(true);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);





  const showSnackbar = (message: string) => {
    // Only show snackbar after initialization to prevent blank notifications
    if (isInitialized && message && message.trim().length > 0) {
      setSnackbarMessage(message);
      setSnackbarVisible(true);
    }
  };

  const initializeChat = useCallback(async () => {
    unifiedLogger.logComponentLifecycle('ChatInterface', 'initialize_start');

    try {
      // Check if server is reachable
      const isReachable = await apiService.isServerReachable();
      setIsConnected(isReachable);

      if (!isReachable) {
        unifiedLogger.warn('ChatInterface', 'server_not_reachable', {
          message: 'Unable to connect to server during initialization'
        });
        showSnackbar('⚠️ Server offline - will retry on first message');
      }

      let currentSessionId = await apiService.getSessionIdAsync();

      if (currentSessionId === 'unknown' || !currentSessionId) {
        const newSessionId = await apiService.resetSession();
        setSessionId(newSessionId);
      } else {
        setSessionId(currentSessionId);
      }

      const welcomeMessage: IMessage = {
        _id: Math.random().toString(),
        text: 'Welcome to FinanceGPT! 🏛️\n\nI can help you with:\n• 👥 Employee records and salary information\n• 💰 Budget data and expenditure analysis\n• 🏗️ PSDP project details and tracking\n• 📄 Document analysis and insights\n\nWhat would you like to know?',
        createdAt: new Date(),
        user: botUser,
        system: false,
      };

      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Error initializing chat:', error);
    }

    unifiedLogger.logComponentLifecycle('ChatInterface', 'initialize_complete');
  }, []);



  const onSend = useCallback(async (newMessages: IMessage[] = []) => {
    const userMessage = newMessages[0];
    if (!userMessage?.text?.trim()) return;

    // Add user message immediately
    setMessages(prev => GiftedChat.append(prev, newMessages));

    // Show typing indicator but DON'T block input
    setIsTyping(true);

    try {
      const response = await apiService.sendMessage(userMessage.text);

      const hasDownload = !!(response.download_url || response.file_path || response.download_ready);
      setHasDownloadableData(hasDownload);
      setCurrentDownloadData(response);

      if (hasDownload && (response.file_path || response.download_url)) {
        const sessionId = apiService.getSessionId();
        if (sessionId) {
          const fileFormat = response.file_type || (response.filename && response.filename.split('.').pop()) || 'excel';
          const filePath = response.file_path || response.download_url;
          const fileName = response.filename || `financegpt_data_${sessionId}.${fileFormat}`;
          if (filePath) {
            setTimeout(() => fileAttachmentHandler.createFileAttachment(filePath, fileName, fileFormat, sessionId), 1500);
          }
        }
      }

      const responseText = response.response || (typeof response === 'string' ? response : 'I received your message but couldn\'t process it properly. Please try again.');
      const botMessageId = Math.random().toString();
      const botMessage: IMessage = {
        _id: botMessageId,
        text: responseText + (hasDownload && response.file_path ? '\n\n📎 File will be delivered automatically...' : ''),
        createdAt: new Date(),
        user: botUser,
        // quickReplies: {
        //   type: 'radio',
        //   keepIt: true,
        //   values: [{ title: '💬 Ask Another Question', value: 'continue_chat' }],
        // },
      };

      // Mark this message for animation
      setAnimatedMessages(prev => new Set([...prev, botMessageId]));
      setMessages(prev => GiftedChat.append(prev, [botMessage]));
      showSnackbar('✅ Response received');

    } catch (error: any) {
      // Create user-friendly error messages based on error type
      let errorText = 'Sorry, I encountered an error.';
      let snackbarText = 'Request failed';

      if (error.code === 522) {
        errorText = '⏱️ **Server Timeout**\n\nThe server took too long to respond. This usually means:\n\n• The server is busy processing other requests\n• Your query is complex and needs more time\n\n**You can:**\n• Try your message again\n• Wait a minute and retry\n• Simplify your question';
        snackbarText = '⏱️ Server timeout';
      } else if (error.code === 408 || error.error === 'Timeout') {
        errorText = '⏱️ **Request Timeout**\n\nThe request timed out after 15 seconds.\n\n**You can:**\n• Retry your message\n• Check if the server is up\n• Try a simpler question';
        snackbarText = '⏱️ Timeout';
      } else if (error.code === 0 || error.error === 'Network Error') {
        errorText = '📡 **No Connection**\n\nCannot reach the server.\n\n**Check:**\n✓ Internet connection\n✓ WiFi/mobile data enabled\n✓ Server status\n\nThen retry your message.';
        snackbarText = '📡 No connection';
      } else if (error.message === 'No internet connection') {
        errorText = '📡 **Offline**\n\nNo internet connection detected.\n\n**Action needed:**\n✓ Connect to WiFi, or\n✓ Enable mobile data\n\nThen send your message again.';
        snackbarText = '📡 Offline';
      } else {
        errorText = `❌ **Error**\n\n${error.message || 'Unable to connect to server'}.\n\nYou can retry your message or start a new session.`;
        snackbarText = '❌ Failed';
      }

      const errorMessage: IMessage = {
        _id: Math.random().toString(),
        text: errorText,
        createdAt: new Date(),
        user: botUser,
      };
      setMessages(prev => GiftedChat.append(prev, [errorMessage]));
      showSnackbar(snackbarText);
    } finally {
      // Remove typing indicator - user can send messages again
      setIsTyping(false);
    }
  }, [isConnected]);

  // Initialize component handlers

  const fileAttachmentHandler = useFileAttachmentHandler({
    onFileAttachment: (message: IMessage) => {
      setMessages(prev => GiftedChat.append(prev, [message]));
    },
    showSnackbar,
  });

  const quickReplyHandler = useQuickReplyHandler({
    onSend,
    showSnackbar,
    setHasDownloadableData,
    setCurrentDownloadData,
    onNewSession,
    handleFileOperation: fileAttachmentHandler.handleFileOperation,
    isProcessingFile,
    setIsProcessingFile,
  });





  const handleNewSession = async () => {
    try {
      // Create new session
      await apiService.createNewSession();

      setMessages([
        {
          _id: Math.random().toString(),
          text: 'New session started! How can I help you with government financial data?',
          createdAt: new Date(),
          user: botUser,
        },
      ]);
      setHasDownloadableData(false);
      showSnackbar('New session started');
      onNewSession?.();
    } catch (error) {
      showSnackbar('Failed to start new session');
    }
  };



  const renderFooter = () => {
    return <TypingIndicator isTyping={isTyping} />;
  };

  // Handle animation completion
  const handleAnimationComplete = useCallback((messageId: string) => {
    setAnimatedMessages(prev => {
      const newSet = new Set(prev);
      newSet.delete(messageId);
      return newSet;
    });
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="auto" backgroundColor={Colors.primary} />

      {/* Custom Header with Gradient */}
      <LinearGradient
        colors={['rgba(0, 0, 0, 0.95)', 'rgba(0, 0, 0, 0.85)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.customHeader}
      >
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => setMenuVisible(true)}
          accessibilityLabel="Open menu"
        >
          <Text style={styles.menuButtonText}>☰</Text>
        </TouchableOpacity>
        {/* Connection status indicator */}
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: isConnected ? '#10b981' : '#ef4444' }]} />
          <Text style={styles.statusText}>{isConnected ? 'Online' : 'Offline'}</Text>
        </View>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleNewSession}
          accessibilityLabel="Start new session"
        >
          <Text style={styles.refreshButtonText}>↻</Text>
        </TouchableOpacity>
      </LinearGradient>

      {/* Menu Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuVisible(false)}
        >
          <LinearGradient
            colors={['rgba(0, 0, 0, 0.98)', 'rgba(0, 0, 0, 0.95)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.menuContainer}
          >
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setMenuVisible(false);
                handleNewSession();
              }}
            >
              <Text style={styles.menuItemIcon}>💬</Text>
              <Text style={styles.menuItemText}>New Chat</Text>
            </TouchableOpacity>
          </LinearGradient>
        </TouchableOpacity>
      </Modal>

      {/* Custom Chat UI with all features */}
      <KeyboardAvoidingView
        style={{ flex: 1, backgroundColor: Colors.background }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={0}
      >
        {/* Messages */}
        <ScrollView
          style={{ flex: 1, backgroundColor: ThemeColors.background }}
          contentContainerStyle={{ flexGrow: 1, justifyContent: 'flex-end', paddingTop: 10 }}
          keyboardShouldPersistTaps="handled"
        >
          {messages.slice().reverse().map((msg, index) => {
            const isUser = msg.user._id === 1;
            const hasQuickReplies = msg.quickReplies && msg.quickReplies.values && msg.quickReplies.values.length > 0;
            const shouldAnimate = !isUser && animatedMessages.has(msg._id.toString());

            // Determine if this is the first/last message in a group (for avatar display)
            const nextMsg = messages.slice().reverse()[index + 1];
            const isLastInGroup = !nextMsg || nextMsg.user._id !== msg.user._id;

            return (
              <View key={msg._id}>
                {/* Animated Message Bubble */}
                <AnimatedMessageBubble
                  message={msg}
                  isUser={isUser}
                  showAvatar={!isUser}
                  isLastInGroup={isLastInGroup}
                  enableAnimation={shouldAnimate}
                  animationSpeed={25}
                  onAnimationComplete={() => handleAnimationComplete(msg._id.toString())}
                />

                {/* File Preview Card (replaces quick replies for files) */}
                {hasQuickReplies && !isUser && (
                  <View style={{ paddingHorizontal: 16, marginTop: -4, marginBottom: 8 }}>
                    <FilePreviewCard
                      fileName={
                        msg.quickReplies!.values[0]?.value?.split('|')[1] ||
                        'financegpt_data.xlsx'
                      }
                      fileType={
                        (msg.quickReplies!.values[0]?.value?.split('|')[2] as 'xlsx' | 'pdf' | 'csv') ||
                        'xlsx'
                      }
                      fileSize="245 KB"
                      onDownload={() => {
                        const downloadReply = msg.quickReplies!.values.find(r =>
                          r.value.startsWith('download_file')
                        );
                        if (downloadReply) {
                          quickReplyHandler.handleQuickReply([downloadReply]);
                        }
                      }}
                      onShare={() => {
                        const shareReply = msg.quickReplies!.values.find(r =>
                          r.value.startsWith('share_file')
                        );
                        if (shareReply) {
                          quickReplyHandler.handleQuickReply([shareReply]);
                        }
                      }}
                    />
                  </View>
                )}
              </View>
            );
          })}

          {/* Modern Typing Indicator */}
          {isTyping && <ModernTypingIndicator showAvatar={true} />}
        </ScrollView>

        {/* Modern Input Bar */}
        <ModernInputBar
          value={inputText}
          onChangeText={setInputText}
          onSend={() => {
            if (inputText.trim()) {
              Keyboard.dismiss();
              const newMessage: IMessage = {
                _id: Math.random().toString(),
                text: inputText.trim(),
                createdAt: new Date(),
                user: user,
              };
              onSend([newMessage]);
              setInputText('');
            }
          }}
          placeholder="Message FinanceGPT..."
          disabled={isTyping}
        />
      </KeyboardAvoidingView>

      {/* Snackbar for notifications */}
      {/* <ChatSnackbar
        visible={snackbarVisible}
        message={snackbarMessage}
        onDismiss={() => setSnackbarVisible(false)}
      /> */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ThemeColors.background,
  },
  keyboardContainer: {
    flex: 1,
    backgroundColor: ThemeColors.background,
  },
  customHeader: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 0, // No left padding - image at far left edge
    paddingRight: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerImage: {
    height: 40,
    width: 200,
    marginRight: 'auto', // Push image to far left
  },
  headerRightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuButtonText: {
    color: Colors.textOnPrimary,
    fontSize: 24,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshButtonText: {
    color: Colors.textOnPrimary,
    fontSize: 24,
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    color: Colors.textOnPrimary,
    fontSize: 12,
    fontWeight: '500',
  },
  // chatContainer: {

  //   flex: 1,
  //   backgroundColor: Colors.background,
  //   backgroundColor:'green'
  // },
  messagesList: {
    backgroundColor: 'transparent',
  },
  messagesContainer: {
    paddingBottom: Platform.OS === 'ios' ? 8 : 4,
    backgroundColor: Colors.background,
  },

  loadingContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
    // backgroundColor: 'yellow'
  },
  // Legacy styles (kept for compatibility)
  connectionIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  connectionText: {
    fontSize: 12,
    color: Colors.textOnPrimary,
    opacity: 0.8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    paddingTop: 60,
  },
  menuContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
  },
  menuItemIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
