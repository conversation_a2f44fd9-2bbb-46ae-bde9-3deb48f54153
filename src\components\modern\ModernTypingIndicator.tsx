/**
 * ModernTypingIndicator Component
 * Animated typing indicator with pulsing dots
 * Optimized for both iOS and Android
 * Uses standard React Native Animated API for better compatibility
 */

import React, { memo, useEffect, useRef } from 'react';
import { View, StyleSheet, Image, Platform, Animated } from 'react-native';
import { Colors, Spacing, BorderRadius, Shadows } from '../../theme';

const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

interface ModernTypingIndicatorProps {
  showAvatar?: boolean;
}

const AnimatedDot = ({ delay }: { delay: number }) => {
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0.4)).current;

  useEffect(() => {
    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.timing(scale, {
          toValue: 1.2,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ])
    );

    const opacityAnimation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.4,
          duration: 400,
          useNativeDriver: true,
        }),
      ])
    );

    scaleAnimation.start();
    opacityAnimation.start();

    return () => {
      scaleAnimation.stop();
      opacityAnimation.stop();
    };
  }, [delay, scale, opacity]);

  return (
    <Animated.View
      style={[
        styles.dot,
        {
          transform: [{ scale }],
          opacity,
        },
      ]}
    />
  );
};

export const ModernTypingIndicator = memo<ModernTypingIndicatorProps>(
  ({ showAvatar = true }) => {
    return (
      <View style={styles.container}>
        {showAvatar && (
          <View style={styles.avatarContainer}>
            <Image source={CHAT_AVATAR} style={styles.avatar} resizeMode="cover" />
          </View>
        )}
        <View style={styles.bubble}>
          <View style={styles.dotsContainer}>
            <AnimatedDot delay={0} />
            <AnimatedDot delay={150} />
            <AnimatedDot delay={300} />
          </View>
        </View>
      </View>
    );
  }
);

ModernTypingIndicator.displayName = 'ModernTypingIndicator';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    maxWidth: '100%',
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: Spacing.sm,
    overflow: 'hidden',
    backgroundColor: Colors.surface,
  },
  avatar: {
    width: 32,
    height: 32,
  },
  bubble: {
    backgroundColor: Colors.botMessageBg,
    borderRadius: BorderRadius.lg,
    borderBottomLeftRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minWidth: 60,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
        shadowColor: Colors.shadowLight,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.sm,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.textSecondary,
  },
});

