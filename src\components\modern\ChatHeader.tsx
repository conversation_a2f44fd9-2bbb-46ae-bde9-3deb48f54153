/**
 * Chat<PERSON>eader Component
 * ChatGPT-inspired header with hamburger menu
 */

import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Typography, Spacing, Shadows } from '../../theme';
import { useTheme } from '../../contexts/ThemeContext';

const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

interface ChatHeaderProps {
  onMenuPress: () => void;
  onNewChat?: () => void;
  title?: string;
  subtitle?: string;
}

export const ChatHeader = memo<ChatHeaderProps>(
  ({ onMenuPress, onNewChat, title = 'FinanceGPT', subtitle = 'AI Assistant' }) => {
    const { theme } = useTheme();

    return (
      <View style={[styles.container, { backgroundColor: theme.surface, borderBottomColor: theme.border }]}>
        {/* Left: Hamburger Menu */}
        <TouchableOpacity
          onPress={onMenuPress}
          style={styles.menuButton}
          activeOpacity={0.7}
        >
          <Ionicons name="menu" size={24} color={theme.textPrimary} />
        </TouchableOpacity>

        {/* Center: Title & Avatar */}
        <View style={styles.centerContainer}>
          <Image source={CHAT_AVATAR} style={styles.avatar} resizeMode="cover" />
          <View style={styles.titleContainer}>
            <Text style={[styles.title, { color: theme.textPrimary }]}>{title}</Text>
            <Text style={[styles.subtitle, { color: theme.textSecondary }]}>{subtitle}</Text>
          </View>
        </View>

        {/* Right: New Chat Button */}
        {onNewChat && (
          <TouchableOpacity
            onPress={onNewChat}
            style={styles.newChatButton}
            activeOpacity={0.7}
          >
            <Ionicons name="create-outline" size={22} color={theme.textPrimary} />
          </TouchableOpacity>
        )}
      </View>
    );
  }
);

ChatHeader.displayName = 'ChatHeader';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  menuButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  centerContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: Spacing.sm,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  subtitle: {
    fontSize: Typography.fontSize.sm,
    marginTop: 2,
  },
  newChatButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
});

