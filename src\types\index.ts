export interface Message {
  _id: string | number;
  text: string;
  createdAt: Date;
  user: {
    _id: string | number;
    name: string;
    avatar?: string;
  };
  system?: boolean;
  image?: string;
  file?: {
    name: string;
    url: string;
    type: 'pdf' | 'excel' | 'csv';
  };
}

export interface ChatResponse {
  response: string;
  ui_type: 'text' | 'table' | 'file_list';
  actions: string[];
  download_url?: string;
  session_id: string;
  data?: any;
  // Additional properties for file handling
  download_ready?: boolean;
  file_path?: string;
  file_type?: string;
  filename?: string;
}

export interface ApiResponse {
  success: boolean;
  data?: ChatResponse;
  error?: string;
}

export interface SessionData {
  sessionId: string;
  createdAt: string;
  lastActivity: string;
}

export interface DownloadFile {
  name: string;
  url: string;
  type: 'pdf' | 'excel' | 'csv';
  size?: number;
}