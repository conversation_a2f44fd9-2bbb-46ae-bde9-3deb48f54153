/**
 * Modern Color Palette - ChatGPT Inspired
 * Optimized for both iOS and Android
 */

export const DarkTheme = {
  // Background colors
  background: '#343541',
  surface: '#444654',
  surfaceElevated: '#565869',
  
  // Message colors
  userMessageBg: '#10A37F',
  userMessageGradient: ['#10A37F', '#0D8A6A'] as [string, string],
  botMessageBg: '#444654',
  
  // Text colors
  textPrimary: '#ECECF1',
  textSecondary: '#8E8EA0',
  textMuted: '#6B6C7B',
  textOnPrimary: '#FFFFFF',
  
  // Border and divider colors
  border: '#565869',
  divider: '#4A4B57',
  
  // Accent colors
  accent: '#10A37F',
  accentHover: '#0D8A6A',
  accentLight: '#1AB88F',
  primary: '#10A37F',

  // Status colors
  error: '#EF4444',
  errorBg: '#FEE2E2',
  success: '#10B981',
  warning: '#F59E0B',
  info: '#3B82F6',
  
  // Input colors
  inputBg: '#40414F',
  inputBorder: '#565869',
  inputPlaceholder: '#8E8EA0',
  inputFocusBorder: '#10A37F',
  
  // Shadow colors
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.15)',
  shadowHeavy: 'rgba(0, 0, 0, 0.25)',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
};

export const LightTheme = {
  // Background colors
  background: '#FFFFFF',
  surface: '#F7F7F8',
  surfaceElevated: '#FFFFFF',
  
  // Message colors
  userMessageBg: '#10A37F',
  userMessageGradient: ['#10A37F', '#0D8A6A'] as [string, string],
  botMessageBg: '#F7F7F8',
  
  // Text colors
  textPrimary: '#343541',
  textSecondary: '#6B6C7B',
  textMuted: '#8E8EA0',
  textOnPrimary: '#FFFFFF',
  
  // Border and divider colors
  border: '#E5E5E5',
  divider: '#EBEBEB',
  
  // Accent colors
  accent: '#10A37F',
  accentHover: '#0D8A6A',
  accentLight: '#1AB88F',
  primary: '#10A37F',

  // Status colors
  error: '#DC2626',
  errorBg: '#FEE2E2',
  success: '#059669',
  warning: '#D97706',
  info: '#2563EB',
  
  // Input colors
  inputBg: '#FFFFFF',
  inputBorder: '#D1D5DB',
  inputPlaceholder: '#9CA3AF',
  inputFocusBorder: '#10A37F',
  
  // Shadow colors
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowMedium: 'rgba(0, 0, 0, 0.1)',
  shadowHeavy: 'rgba(0, 0, 0, 0.15)',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.4)',
  overlayLight: 'rgba(0, 0, 0, 0.2)',
};

// Export the default theme (Dark)
export const Colors = DarkTheme;

// Type for theme
export type Theme = typeof DarkTheme;

