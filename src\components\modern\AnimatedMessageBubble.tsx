/**
 * AnimatedMessageBubble Component
 * Enhanced message bubble with typewriter animation for AI responses
 * Uses TypewriterText for animated typing effect
 */

import React, { memo, useState, useCallback } from 'react';
import { View, Text, StyleSheet, Image, Platform, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Typography, Spacing, BorderRadius } from '../../theme';
import { TypewriterText } from './TypewriterText';

const CHAT_AVATAR = require('../../../assets/chat-avatar.png');

interface AnimatedMessageBubbleProps {
  message: {
    _id: string | number;
    text: string;
    createdAt: Date | number;
    user: {
      _id: string | number;
      name?: string;
      avatar?: string | number | any;
    };
  };
  isUser: boolean;
  showAvatar?: boolean;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
  enableAnimation?: boolean; // Whether to animate this message
  animationSpeed?: number; // Characters per second
  onAnimationComplete?: () => void;
  onAnimationProgress?: (progress: number) => void;
}

export const AnimatedMessageBubble = memo<AnimatedMessageBubbleProps>(
  ({ 
    message, 
    isUser, 
    showAvatar = true, 
    isFirstInGroup = true, 
    isLastInGroup = true,
    enableAnimation = false,
    animationSpeed = 25, // Slightly slower for better readability
    onAnimationComplete,
    onAnimationProgress,
  }) => {
    const { theme, isDark } = useTheme();
    const [animationCompleted, setAnimationCompleted] = useState(!enableAnimation);

    const handleAnimationComplete = useCallback(() => {
      setAnimationCompleted(true);
      onAnimationComplete?.();
    }, [onAnimationComplete]);

    const handleAnimationProgress = useCallback((currentText: string, progress: number) => {
      onAnimationProgress?.(progress);
    }, [onAnimationProgress]);

    // User messages (no animation needed)
    if (isUser) {
      return (
        <View style={styles.userContainer}>
          <View
            style={[
              styles.userBubble,
              {
                backgroundColor: isDark ? '#2F7C6E' : '#10A37F',
                borderBottomRightRadius: isLastInGroup ? BorderRadius.sm : BorderRadius.xl,
              },
            ]}
          >
            <Text style={styles.userText}>{message.text}</Text>
          </View>
        </View>
      );
    }

    // AI messages (with optional animation)
    return (
      <View style={styles.botContainer}>
        {showAvatar && isLastInGroup ? (
          <View style={styles.avatarContainer}>
            <Image source={CHAT_AVATAR} style={styles.avatar} resizeMode="cover" />
          </View>
        ) : (
          <View style={styles.avatarPlaceholder} />
        )}
        <View
          style={[
            styles.botBubble,
            {
              backgroundColor: isDark ? '#444654' : '#F7F7F8',
              borderBottomLeftRadius: isLastInGroup ? BorderRadius.sm : BorderRadius.xl,
            },
          ]}
        >
          {enableAnimation && !animationCompleted ? (
            <TypewriterText
              text={message.text}
              style={[styles.botText, { color: theme.textPrimary }]}
              speed={animationSpeed}
              onComplete={handleAnimationComplete}
              onCharacterTyped={handleAnimationProgress}
              startDelay={300} // Small delay before starting to type
              testID={`animated-message-${message._id}`}
            />
          ) : (
            <Text style={[styles.botText, { color: theme.textPrimary }]}>
              {message.text}
            </Text>
          )}
        </View>
      </View>
    );
  }
);

AnimatedMessageBubble.displayName = 'AnimatedMessageBubble';

const styles = StyleSheet.create({
  // User message styles
  userContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xs,
    maxWidth: '100%',
  },
  userBubble: {
    maxWidth: '80%',
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  userText: {
    color: '#FFFFFF',
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * 1.5,
    fontWeight: '400',
  },

  // Bot message styles
  botContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xs,
    maxWidth: '100%',
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: Spacing.sm,
    overflow: 'hidden',
    backgroundColor: '#E5E5E5',
  },
  avatarPlaceholder: {
    width: 32,
    marginRight: Spacing.sm,
  },
  avatar: {
    width: 32,
    height: 32,
  },
  botBubble: {
    flex: 1,
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    maxWidth: '85%',
  },
  botText: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * 1.5,
    fontWeight: '400',
  },
});

export default AnimatedMessageBubble;
