import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import ChatInterface from '../screens/chatInterface/ChatInterfaceNew';

export type RootStackParamList = {
  Chat: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Chat"
        screenOptions={{
          headerShown: false, // We handle headers in individual screens
        }}
      >
        <Stack.Screen
          name="Chat"
          component={ChatInterface}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
