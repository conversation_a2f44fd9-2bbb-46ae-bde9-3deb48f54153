// FinanceGPT API Types - Enhanced from FinanceGPT 1

export interface ChatMessage {
  message: string;
  session_id?: string;
  channel: 'mobile';
  device_info?: {
    platform: string;
    version: string;
    app_version: string;
  };
}

export interface ChatResponse {
  response: string;
  session_id: string;
  ui_type?: 'text' | 'table' | 'file_list';
  download_url?: string;
  download_ready?: boolean;
  file_path?: string;
  file_type?: string;
  filename?: string;
  record_count?: number;
  domain?: string;
  success?: boolean;
  actions?: string[];
  metadata?: {
    query_type?: string;
    data_source?: string;
    record_count?: number;
  };
}

export interface DownloadRequest {
  session_id: string;
  format: 'excel' | 'pdf' | 'csv';
  type?: 'basic' | 'ddo' | 'designation';
}

export interface DownloadResponse {
  download_url: string;
  filename: string;
  file_size?: number;
  expires_at?: string;
}

export interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  version: string;
  timestamp: string;
  services?: {
    database: boolean;
    redis: boolean;
    ai_service: boolean;
  };
}

export interface APIError {
  error: string;
  message: string;
  code?: number;
  details?: any;
}

// Chat Message Types for react-native-gifted-chat
export interface GiftedChatMessage {
  _id: string | number;
  text: string;
  createdAt: Date;
  user: GiftedChatUser;
  image?: string;
  video?: string;
  audio?: string;
  system?: boolean;
  sent?: boolean;
  received?: boolean;
  pending?: boolean;
  quickReplies?: {
    type: 'radio' | 'checkbox';
    keepIt?: boolean;
    values: Array<{
      title: string;
      value: string;
      messageId?: string;
    }>;
  };
}

export interface GiftedChatUser {
  _id: string | number;
  name?: string;
  avatar?: string;
}

// Session Management
export interface SessionData {
  session_id: string;
  created_at: string;
  last_activity: string;
  message_count: number;
  user_id?: string;
}

// File Management
export interface DownloadedFile {
  id: string;
  filename: string;
  filepath: string;
  format: 'pdf' | 'excel' | 'csv';
  size: number;
  downloaded_at: string;
  session_id: string;
  metadata?: {
    title?: string;
    description?: string;
    data_type?: string;
  };
}