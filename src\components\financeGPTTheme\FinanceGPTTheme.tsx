import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Platform, Animated, Image } from 'react-native';
import { MD3LightTheme as DefaultTheme } from 'react-native-paper';
import { Bubble, InputToolbar, Send, Composer } from 'react-native-gifted-chat';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/Colors';

// Using default system fonts for now
const fontConfig = undefined;

// FinanceGPT Modern Theme (ChatGPT-like)
export const FinanceGPTTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: Colors.primary,
    secondary: Colors.secondary,
    tertiary: Colors.secondaryLight,
    surface: Colors.surface,
    surfaceVariant: Colors.surfaceDark,
    background: Colors.background,
    error: Colors.error,
    onPrimary: Colors.textOnPrimary,
    onSecondary: Colors.textOnSecondary,
    onSurface: Colors.text,
    onBackground: Colors.text,
    outline: Colors.border,
  },
  // Using default fonts
  roundness: 12, // Modern rounded corners like ChatGPT
};

// FUNCTIONAL COMPONENT - Modern message bubbles (NO ANIMATIONS to avoid hooks issues)
const RenderBubble = (props: any) => {
  return (
    <Bubble
      {...props}
      wrapperStyle={{
        right: {
          backgroundColor: Colors.primary,
          borderRadius: 18,
          borderBottomRightRadius: 4,
          marginVertical: 2,
          marginHorizontal: 8,
          paddingHorizontal: 16,
          paddingVertical: 10,
          maxWidth: '80%',
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
        },
        left: {
          backgroundColor: Colors.surface,
          borderRadius: 18,
          borderBottomLeftRadius: 4,
          marginVertical: 2,
          marginHorizontal: 8,
          paddingHorizontal: 16,
          paddingVertical: 10,
          maxWidth: '80%',
          elevation: 1,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 1.41,
          borderWidth: 1,
          borderColor: Colors.border,
        },
      }}
    textStyle={{
      right: {
        color: Colors.textOnPrimary,
        fontSize: 16,
        lineHeight: 22,
        fontWeight: '400',
      },
      left: {
        color: Colors.text,
        fontSize: 16,
        lineHeight: 22,
        fontWeight: '400',
      },
    }}
    timeTextStyle={{
      right: {
        color: Colors.textOnPrimary,
        opacity: 0.7,
        fontSize: 12,
      },
      left: {
        color: Colors.textSecondary,
        fontSize: 12,
      },
    }}
    usernameStyle={{
      color: Colors.primary,
      fontWeight: '600',
      fontSize: 14,
    }}
  />
  );
};

// Modern Chat Theme Components (ChatGPT-style)
export class ModernChatTheme {

  // Modern message bubbles - uses functional component WITHOUT hooks
  static renderBubble = (props: any) => <RenderBubble {...props} />;

  // Modern input toolbar like ChatGPT
  static renderInputToolbar = (props: any) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={{
          backgroundColor: Colors.surface,
          borderTopWidth: 1,
          borderTopColor: Colors.border,
          paddingHorizontal: 12,
          // paddingVertical: 8,
          marginHorizontal: 8,
          marginBottom: Platform.OS === 'ios' ? 0 : 8,
          borderRadius: 24,
          elevation: 4,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 3.84,
          minHeight: 56, // Fixed minimum height
        }}
        primaryStyle={{
          alignItems: 'flex-end',
          flexDirection: 'row',
          paddingRight: 8,
        }}
      />
    );
  };

  // Modern composer (text input)
  static renderComposer = (props: any) => {
    return (
      <Composer
        {...props}
        textInputStyle={{
          backgroundColor: 'transparent',
          borderRadius: 20,
          borderWidth: 0,
          color: Colors.text,
          fontSize: 16,
          lineHeight: 20,
          paddingHorizontal: 16,
          paddingVertical: Platform.OS === 'ios' ? 12 : 10,
          minHeight: 40, // Fixed minimum height
          maxHeight: 100, // Allow multiline input
          textAlignVertical: 'top',
          flex: 1, // Take available space
        }}
        placeholderTextColor={Colors.textSecondary}
        placeholder="Ask about budget, employees, or PSDP projects..."
      />
    );
  };

  // Modern send button like ChatGPT with stable sizing
  static renderSend = (props: any) => {
    return (
      <Send
        {...props}
        disabled={!props.text?.trim()}
        containerStyle={{
          // backgroundColor: 'green',
          justifyContent: 'center',
          
          alignItems: 'center',
          paddingHorizontal: 4,
          paddingVertical: 4,
          width: 48, // Fixed width to prevent animation
          height: 48, // Fixed height to prevent animation
          marginLeft: 8,
        }}
      >
        <View style={[
          styles.sendButton,
          {
            backgroundColor: props.text?.trim() ? Colors.primary : Colors.textSecondary,
            opacity: props.text?.trim() ? 1 : 0.5,
          }
        ]}>
          <MaterialIcons
            name="send"
            size={20}
            color={Colors.textOnPrimary}
          />
        </View>
      </Send>
    );
  };

  // Modern avatar - NO ANIMATIONS to avoid hooks issues
  static renderAvatar = (props: any) => {
    const isBot = props.currentMessage.user._id !== 1;

    return (
      <View style={styles.avatar}>
        {isBot ? (
          <Image
            source={require('../../../assets/chat-avatar.png')}
            style={styles.avatarImage}
            resizeMode="cover"
          />
        ) : (
          <Text style={styles.avatarText}>👤</Text>
        )}
      </View>
    );
  };


}

const styles = StyleSheet.create({
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    fontSize: 16,
    color: Colors.textOnPrimary,
  },
  avatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
});
