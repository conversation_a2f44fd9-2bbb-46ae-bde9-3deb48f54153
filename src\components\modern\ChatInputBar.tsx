/**
 * ChatInputBar Component
 * ChatGPT-inspired input bar with separated controls
 * Attachment, Voice, and Send buttons in individual bubbles
 */

import React, { memo, useState, useCallback } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import { Typography, Spacing, BorderRadius, Shadows } from '../../theme';
import { useTheme } from '../../contexts/ThemeContext';

interface ChatInputBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onAttachment?: () => void;
  onVoice?: () => void;
  placeholder?: string;
  disabled?: boolean;
  isRecording?: boolean;
}

export const ChatInputBar = memo<ChatInputBarProps>(
  ({
    value,
    onChangeText,
    onSend,
    onAttachment,
    onVoice,
    placeholder = 'Message FinanceGPT...',
    disabled = false,
    isRecording = false,
  }) => {
    const { theme } = useTheme();
    const [isFocused, setIsFocused] = useState(false);

    const handleSend = useCallback(() => {
      if (value.trim().length === 0 || disabled) return;

      // Haptic feedback
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      onSend();
    }, [value, disabled, onSend]);

    const handleAttachment = useCallback(() => {
      if (disabled) return;
      
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } else {
        Haptics.selectionAsync();
      }
      
      onAttachment?.();
    }, [disabled, onAttachment]);

    const handleVoice = useCallback(() => {
      if (disabled) return;
      
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } else {
        Haptics.selectionAsync();
      }
      
      onVoice?.();
    }, [disabled, onVoice]);

    const canSend = value.trim().length > 0 && !disabled;

    return (
      <View style={[styles.container, { backgroundColor: theme.background, borderTopColor: theme.border }]}>
        <View style={styles.innerContainer}>
            {/* Attachment Button */}
            {onAttachment && (
              <TouchableOpacity
                onPress={handleAttachment}
                disabled={disabled}
                style={[
                  styles.actionButton,
                  { backgroundColor: theme.surface },
                  disabled && styles.actionButtonDisabled,
                ]}
                activeOpacity={0.7}
              >
                <Ionicons
                  name="add-circle"
                  size={28}
                  color={disabled ? theme.textMuted : theme.textSecondary}
                />
              </TouchableOpacity>
            )}

            {/* Input Container */}
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: theme.inputBg,
                  borderColor: isFocused ? theme.inputFocusBorder : theme.inputBorder,
                },
              ]}
            >
              <TextInput
                style={[styles.input, { color: theme.textPrimary }]}
                value={value}
                onChangeText={onChangeText}
                placeholder={placeholder}
                placeholderTextColor={theme.inputPlaceholder}
                multiline
                maxLength={2000}
                editable={!disabled}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                returnKeyType="default"
                blurOnSubmit={false}
              />
            </View>

            {/* Voice/Send Button */}
            {canSend ? (
              <TouchableOpacity
                onPress={handleSend}
                style={styles.sendButtonContainer}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={theme.userMessageGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.sendButton}
                >
                  <Ionicons name="arrow-up" size={22} color={theme.textOnPrimary} />
                </LinearGradient>
              </TouchableOpacity>
            ) : (
              onVoice && (
                <TouchableOpacity
                  onPress={handleVoice}
                  disabled={disabled}
                  style={[
                    styles.voiceButton,
                    { backgroundColor: isRecording ? theme.errorBg : theme.surface },
                    disabled && styles.actionButtonDisabled,
                  ]}
                  activeOpacity={0.7}
                >
                  <Ionicons
                    name={isRecording ? 'stop-circle' : 'mic'}
                    size={24}
                    color={
                      isRecording
                        ? theme.error
                        : disabled
                        ? theme.textMuted
                        : theme.textSecondary
                    }
                  />
                </TouchableOpacity>
              )
            )}
        </View>
      </View>
    );
  }
);

ChatInputBar.displayName = 'ChatInputBar';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderTopWidth: 1,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: Spacing.sm,
  },
  actionButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  inputContainer: {
    flex: 1,
    borderRadius: BorderRadius['2xl'],
    borderWidth: 1,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    minHeight: 44,
    maxHeight: 120,
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  input: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    paddingTop: Platform.OS === 'ios' ? 8 : 4,
    paddingBottom: Platform.OS === 'ios' ? 8 : 4,
    ...Platform.select({
      android: {
        textAlignVertical: 'center',
      },
    }),
  },
  sendButtonContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        ...Shadows.md,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sendButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    ...Platform.select({
      ios: {
        ...Shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
});

