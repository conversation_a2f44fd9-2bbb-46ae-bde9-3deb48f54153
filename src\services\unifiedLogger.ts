/**
 * Unified Logger - Simple, clean logging for React Native
 * Outputs logs to both mobile console and terminal for easy debugging
 */

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'PERF';
  component: string;
  action: string;
  details?: any;
}

export class UnifiedLogger {
  private static instance: UnifiedLogger;
  private logs: LogEntry[] = [];
  private maxLogs = 200;
  private subscribers: ((entry: LogEntry) => void)[] = [];

  private constructor() {
    console.log('🔧 Unified Logger initialized');
  }

  public static getInstance(): UnifiedLogger {
    if (!UnifiedLogger.instance) {
      UnifiedLogger.instance = new UnifiedLogger();
    }
    return UnifiedLogger.instance;
  }

  /**
   * Core logging method - outputs to both mobile console and terminal
   */
  private log(level: LogEntry['level'], component: string, action: string, details?: any) {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      id: Math.random().toString(36).substr(2, 9),
      timestamp,
      level,
      component,
      action,
      details: details ? { ...details } : undefined,
    };

    // Store in memory
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Output to console (both mobile and terminal)
    this.outputToConsole(logEntry);

    // Notify subscribers (for any UI components that need it)
    this.subscribers.forEach(callback => callback(logEntry));
  }

  private outputToConsole(entry: LogEntry) {
    const { timestamp, level, component, action, details } = entry;
    
    // Format: [timestamp] [level] [component] action details
    let logMessage = `[${timestamp}] [${level}] [${component}] ${action}`;
    
    if (details && Object.keys(details).length > 0) {
      logMessage += ' ' + JSON.stringify(details);
    }
    
    // Output to console (appears in both mobile and terminal)
    console.log(logMessage);
  }

  // Convenience methods for different log levels
  public debug(component: string, action: string, details?: any) {
    this.log('DEBUG', component, action, details);
  }

  public info(component: string, action: string, details?: any) {
    this.log('INFO', component, action, details);
  }

  public warn(component: string, action: string, details?: any) {
    this.log('WARN', component, action, details);
  }

  public error(component: string, action: string, details?: any) {
    this.log('ERROR', component, action, details);
  }

  public perf(component: string, action: string, details?: any) {
    this.log('PERF', component, action, details);
  }

  // Specific logging methods for common scenarios
  public logComponentLifecycle(component: string, lifecycle: string, details?: any) {
    this.info(component, `component_${lifecycle}`, details);
  }

  public logUserInteraction(component: string, interaction: string, details?: any) {
    this.info(component, interaction, details);
  }

  public logAPI(component: string, apiAction: string, details?: any) {
    this.info(component, apiAction, details);
  }

  public logFileOperation(component: string, operation: string, details?: any) {
    this.info(component, operation, details);
  }

  public logPerformance(component: string, metric: string, duration?: number, details?: any) {
    const perfDetails = {
      ...(details || {}),
      ...(duration !== undefined && { duration })
    };
    this.perf(component, metric, perfDetails);
  }

  public logStateChange(component: string, change: string, details?: any) {
    this.debug(component, `state_${change}`, details);
  }

  // Utility methods
  public getRecentLogs(count: number = 50): LogEntry[] {
    return this.logs.slice(-count);
  }

  public getLogsByComponent(component: string, count: number = 20): LogEntry[] {
    return this.logs
      .filter(entry => entry.component === component)
      .slice(-count);
  }

  public getLogsByLevel(level: LogEntry['level'], count: number = 20): LogEntry[] {
    return this.logs
      .filter(entry => entry.level === level)
      .slice(-count);
  }

  public getErrorLogs(count: number = 20): LogEntry[] {
    return this.getLogsByLevel('ERROR', count);
  }

  public getPerformanceLogs(count: number = 20): LogEntry[] {
    return this.getLogsByLevel('PERF', count);
  }

  public clearLogs() {
    this.logs = [];
    console.log('🗑️  Logs cleared');
  }

  public subscribe(callback: (entry: LogEntry) => void): () => void {
    this.subscribers.push(callback);
    return () => {
      this.subscribers = this.subscribers.filter(cb => cb !== callback);
    };
  }

  // Method to get logs as formatted string for easy copy-paste
  public getFormattedLogs(count: number = 50): string {
    return this.logs
      .slice(-count)
      .map(entry => {
        let logLine = `[${entry.timestamp}] [${entry.level}] [${entry.component}] ${entry.action}`;
        if (entry.details && Object.keys(entry.details).length > 0) {
          logLine += ' ' + JSON.stringify(entry.details);
        }
        return logLine;
      })
      .join('\n');
  }
}

// Create and export singleton instance
export const unifiedLogger = UnifiedLogger.getInstance();

export default unifiedLogger;