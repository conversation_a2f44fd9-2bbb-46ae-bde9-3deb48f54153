import { Alert, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { Paths, StorageAccessFramework } from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './api';
import { DownloadedFile } from '../types/api';

export class FileDownloadService {
  private static downloadsDirectory: string;

  static initialize(): void {
    // Use proper cache directory path
    this.downloadsDirectory = Paths.cache + 'downloads/';
  }

  static async downloadAndViewFile(
    sessionId: string,
    format: 'excel' | 'pdf' | 'csv',
    filename?: string
  ): Promise<void> {
    try {
      // Show initial progress
      Alert.alert(
        'FinanceGPT Download',
        `Preparing ${format.toUpperCase()} file...`,
        [{ text: 'Cancel', style: 'cancel' }]
      );

      // Get download URL from FinanceGPT API
      const downloadResponse = await apiService.downloadFile(format);
      console.log('📥 Download response:', downloadResponse);

      // Generate filename
      const fileExtension = format === 'excel' ? 'xlsx' : format;
      const defaultFilename = filename || downloadResponse.filename || `financegpt_${sessionId}.${fileExtension}`;
      
      let destinationUri: string;
      
      // For Android, try to save to Downloads directory using Storage Access Framework
      if (Platform.OS === 'android') {
        try {
          // Request permissions to access external storage
          const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync();
          
          if (permissions.granted) {
            // Create file in the selected directory (Downloads)
            const mimeTypes = {
              pdf: 'application/pdf',
              excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              csv: 'text/csv',
            };
            
            const fileUri = await StorageAccessFramework.createFileAsync(
              permissions.directoryUri,
              defaultFilename,
              mimeTypes[format]
            );
            
            // Download the file content
            const downloadResult = await FileSystem.downloadAsync(
              downloadResponse.download_url,
              fileUri
            );
            
            destinationUri = fileUri;
            console.log('📁 File saved to Downloads:', fileUri);
          } else {
            // Fallback to cache directory if permissions not granted
            throw new Error('Storage permissions not granted, falling back to cache');
          }
        } catch (storageError) {
          console.warn('Storage Access Framework failed, using cache:', storageError);
          // Fallback to cache directory
          const downloadsDir = Paths.cache + 'downloads/';
          await FileSystem.makeDirectoryAsync(downloadsDir, { intermediates: true });
          
          const fileUri = downloadsDir + defaultFilename;
          await FileSystem.downloadAsync(downloadResponse.download_url, fileUri);
          destinationUri = fileUri;
        }
      } else {
        // iOS - use cache directory
        const downloadsDir = Paths.cache + 'downloads/';
        await FileSystem.makeDirectoryAsync(downloadsDir, { intermediates: true });
        
        const fileUri = downloadsDir + defaultFilename;
        await FileSystem.downloadAsync(downloadResponse.download_url, fileUri);
        destinationUri = fileUri;
      }

      // Save file metadata
      await this.saveFileMetadata({
        id: sessionId + '_' + format,
        filename: defaultFilename,
        filepath: destinationUri,
        format: format as 'pdf' | 'excel' | 'csv',
        size: downloadResponse.file_size || 0,
        downloaded_at: new Date().toISOString(),
        session_id: sessionId,
        metadata: {
          title: `FinanceGPT ${format.toUpperCase()} Report`,
          description: 'Government financial data export',
          data_type: 'financial_report',
        },
      });

      // Show success dialog
      Alert.alert(
        'Download Complete! ✅',
        `${format.toUpperCase()} file downloaded successfully`,
        [
          {
            text: 'View File',
            onPress: () => this.openFile(destinationUri, format),
          },
          {
            text: 'Share',
            onPress: () => this.shareFile(destinationUri),
          },
          { text: 'OK', style: 'default' },
        ]
      );

    } catch (error: any) {
      console.error('Download error:', error);
      Alert.alert(
        'Download Failed ❌',
        error.message || 'Unable to download the file. Please check your connection and try again.',
        [
          {
            text: 'Retry',
            onPress: () => this.downloadAndViewFile(sessionId, format, filename),
          },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  }

  // Helper method to convert ArrayBuffer to Base64
  private static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private static async openFile(fileUri: string, format: 'excel' | 'pdf' | 'csv'): Promise<void> {
    try {
      const canShare = await Sharing.isAvailableAsync();

      if (canShare) {
        const mimeTypes = {
          pdf: 'application/pdf',
          excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          csv: 'text/csv',
        };

        await Sharing.shareAsync(fileUri, {
          mimeType: mimeTypes[format],
          dialogTitle: `Open ${format.toUpperCase()} file`,
          UTI: format === 'pdf' ? 'com.adobe.pdf' : undefined,
        });
      } else {
        Alert.alert(
          'File Downloaded',
          `${format.toUpperCase()} file saved successfully. You can find it in your downloads folder.`
        );
      }
    } catch (error: any) {
      console.error('Error opening file:', error);
      Alert.alert(
        'Error',
        'Unable to open the file. The file has been saved to your device.'
      );
    }
  }

  private static async shareFile(fileUri: string): Promise<void> {
    try {
      const canShare = await Sharing.isAvailableAsync();

      if (canShare) {
        await Sharing.shareAsync(fileUri, {
          dialogTitle: 'Share FinanceGPT Report',
        });
      } else {
        Alert.alert(
          'Sharing Not Available',
          'File sharing is not available on this device'
        );
      }
    } catch (error: any) {
      console.error('Error sharing file:', error);
      Alert.alert('Error', 'Unable to share the file');
    }
  }

  private static async saveFileMetadata(fileData: DownloadedFile): Promise<void> {
    try {
      const existingFiles = await this.getDownloadedFiles();
      const updatedFiles = [...existingFiles, fileData];
      await AsyncStorage.setItem('financegpt_downloaded_files', JSON.stringify(updatedFiles));
    } catch (error) {
      console.warn('Failed to save file metadata:', error);
    }
  }

  static async getDownloadedFiles(): Promise<DownloadedFile[]> {
    try {
      const filesJson = await AsyncStorage.getItem('financegpt_downloaded_files');
      return filesJson ? JSON.parse(filesJson) : [];
    } catch (error) {
      console.warn('Failed to get downloaded files:', error);
      return [];
    }
  }

  static async clearDownloadHistory(): Promise<void> {
    try {
      await AsyncStorage.removeItem('financegpt_downloaded_files');
    } catch (error) {
      console.warn('Failed to clear download history:', error);
    }
  }

  static async getFileMetadata(fileId: string): Promise<DownloadedFile | null> {
    try {
      const metadataKey = `file_metadata_${fileId}`;
      const metadata = await AsyncStorage.getItem(metadataKey);
      return metadata ? JSON.parse(metadata) : null;
    } catch (error) {
      console.warn('Failed to get file metadata:', error);
      return null;
    }
  }

  static async deleteFile(fileId: string): Promise<boolean> {
    try {
      const metadata = await this.getFileMetadata(fileId);
      if (metadata) {
        await FileSystem.deleteAsync(metadata.filepath, { idempotent: true });
        const metadataKey = `file_metadata_${fileId}`;
        await AsyncStorage.removeItem(metadataKey);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }
}