# 🌓 Quick Theme Guide

## How to Change Theme

### Step 1: Open Menu
Tap the **hamburger icon** (☰) in the top-left corner

### Step 2: Open Settings
Tap **"Settings"** in the sidebar menu

### Step 3: Choose Theme
Tap one of the three options:

```
┌─────────────────────────────────┐
│  ☀️  Light                      │  ← Always light theme
│     Clean and bright            │
├─────────────────────────────────┤
│  🌙  Dark                    ✓  │  ← Always dark theme
│     Easy on the eyes            │
├─────────────────────────────────┤
│  📱  Auto                       │  ← Follows system
│     Match system theme          │
└─────────────────────────────────┘
```

### Step 4: Done!
Theme changes **instantly** - no restart needed!

---

## Theme Modes

### ☀️ Light Mode
- **When to use**: Daytime, bright environments
- **Best for**: Outdoor use, well-lit rooms
- **Battery**: Normal usage
- **Eye strain**: Lower in bright light

### 🌙 Dark Mode (Default)
- **When to use**: Nighttime, dark environments
- **Best for**: Low-light conditions, bedtime
- **Battery**: Saves battery on OLED screens
- **Eye strain**: Lower in dark environments

### 📱 Auto Mode (Recommended)
- **When to use**: All day
- **Best for**: Automatic switching
- **Battery**: Optimized for time of day
- **Eye strain**: Always optimal

---

## Quick Tips

✅ **Auto mode is recommended** - it automatically switches based on your device settings

✅ **Theme preference is saved** - your choice persists across app restarts

✅ **Changes are instant** - no need to restart the app

✅ **All screens adapt** - every part of the app changes with the theme

---

## Keyboard Shortcuts (for developers)

```typescript
// In your code:
const { theme, setThemeMode, toggleTheme } = useTheme();

// Set specific theme
setThemeMode('light');  // Light mode
setThemeMode('dark');   // Dark mode
setThemeMode('auto');   // Auto mode

// Toggle between light/dark
toggleTheme();

// Check current theme
console.log(theme.background); // Current background color
```

---

## Color Reference

### Dark Theme Colors
```
Background:  #343541 (Dark gray)
Surface:     #444654 (Lighter gray)
Primary:     #10A37F (Teal)
Text:        #ECECF1 (Light gray)
```

### Light Theme Colors
```
Background:  #FFFFFF (White)
Surface:     #F7F7F8 (Light gray)
Primary:     #10A37F (Teal)
Text:        #343541 (Dark gray)
```

---

## Troubleshooting

### Theme not changing?
1. Make sure you're in the Settings screen
2. Tap the theme option again
3. Close and reopen the app

### Auto mode not working?
1. Check your device's system theme settings
2. Make sure Auto mode is selected in app
3. Change your device theme to test

### Colors look wrong?
1. Try switching to a different theme
2. Close and reopen the app
3. Clear app cache if needed

---

## That's it! 🎉

Your app now has **professional light/dark mode support**!

Enjoy your personalized experience! 🌓

