# FinanceGPT2 - Modern Government Finance Chatbot

A modern React Native mobile application for government finance management with AI-powered chat interface, data visualization, and file export capabilities.

## Features

### 🤖 AI-Powered Chat Interface
- Modern chat interface using `react-native-gifted-chat`
- Real-time messaging with typing indicators
- Quick reply buttons for common actions
- File upload and download support
- Session management with persistent chat history

### 📊 Data Visualization
- Interactive data tables for government finance data
- Support for budget analysis, employee records, and PSDP projects
- Export functionality for Excel and PDF formats
- Responsive table design with horizontal/vertical scrolling

### 📁 File Management
- Upload documents for analysis
- Download reports in Excel and PDF formats
- Share files via native sharing
- Open files in external applications
- Secure file storage with proper permissions

### 🎨 Government-Themed UI
- Professional color scheme suitable for government use
- Clean, modern interface design
- Responsive layout for different screen sizes
- Accessibility features

### 🔧 Technical Features
- TypeScript for type safety
- Expo SDK for cross-platform development
- AsyncStorage for local data persistence
- Axios for API communication
- Error handling and network status monitoring

## Architecture

### Project Structure
```
src/
├── components/          # React components
│   ├── ChatInterface.tsx    # Main chat interface
│   ├── DataTable.tsx         # Data visualization component
│   └── LoadingIndicator.tsx  # Loading states
├── services/           # Business logic
│   ├── api.ts         # API communication
│   └── fileService.ts # File operations
├── constants/          # App constants
│   ├── Colors.ts      # Color palette
│   └── Config.ts      # Configuration
└── types/             # TypeScript definitions
    └── index.ts       # Type definitions
```

### Key Components

#### ChatInterface
- Main chat component with Gifted Chat integration
- Handles message sending/receiving
- Manages file uploads and downloads
- Displays data tables and loading states

#### DataTable
- Renders government finance data in table format
- Export functionality for Excel and PDF
- Responsive design with scrolling
- Government-styled headers and cells

#### FileService
- Manages file operations (download, share, open)
- Handles different file types (PDF, Excel, images)
- Integrates with native file systems
- Provides secure file storage

## Setup and Installation

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- Android Studio or Xcode (for mobile development)

### Installation
1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

4. Start the development server:
   ```bash
   npm start
   ```

5. Run on your preferred platform:
   - Android: Press `a` in the terminal
   - iOS: Press `i` in the terminal
   - Web: Press `w` in the terminal

### Configuration
Update the `src/constants/Config.ts` file with your backend API details:

```typescript
export const Config = {
  API: {
    BASE_URL: 'https://your-backend-api.com',
    ENDPOINTS: {
      CHAT: '/api/chat',
      UPLOAD: '/api/upload',
      DOWNLOAD: '/api/download',
    },
  },
  // ... other configurations
};
```

## API Integration

### Backend Requirements
Your backend API should support:

#### Chat Endpoint (`POST /api/chat`)
```json
{
  "message": "user message",
  "session_id": "session-123",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

Response:
```json
{
  "type": "text|data|file",
  "response": "bot response",
  "data": [...], // for data type
  "title": "Data Title", // for data type
  "file_url": "download-url", // for file type
  "file_name": "report.pdf", // for file type
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### File Upload (`POST /api/upload`)
- Multipart form data with file
- Returns file metadata and analysis results

#### File Download (`GET /api/download`)
- Secure file download with session validation
- Support for Excel and PDF formats

## File Handling

### Supported File Types
- PDF documents
- Excel spreadsheets (.xlsx)
- Images (for document analysis)
- Text files

### File Operations
1. **Upload**: Users can upload documents via the attachment button
2. **Download**: Files sent by the backend can be downloaded
3. **Share**: Downloaded files can be shared via native sharing
4. **Open**: Files can be opened in external applications

## Permissions

The app requires the following permissions:

### iOS
- `NSDocumentPickerUsageDescription`: Document access for file uploads
- `UISupportsDocumentBrowser`: Document browser support
- `LSSupportsOpeningDocumentsInPlace`: Open documents in place

### Android
- `READ_EXTERNAL_STORAGE`: Read files from device
- `WRITE_EXTERNAL_STORAGE`: Save downloaded files
- `INTERNET`: Network access for API calls
- `ACCESS_NETWORK_STATE`: Network status monitoring

## Security Features

- Session-based authentication
- Secure file handling with proper permissions
- Input validation and sanitization
- Network request encryption
- Local data encryption with AsyncStorage

## Development

### Available Scripts
- `npm start`: Start development server
- `npm run android`: Run on Android
- `npm run ios`: Run on iOS
- `npm run web`: Run on web browser

### Testing
- Unit tests for service functions
- Component testing with React Testing Library
- Integration tests for API calls

### Building for Production
1. Configure production API endpoints in `Config.ts`
2. Build for your target platform:
   ```bash
   expo build:android
   expo build:ios
   ```

## Troubleshooting

### Common Issues
1. **Metro bundler not starting**: Check if port 8081 is available
2. **File upload not working**: Verify permissions in app.json
3. **API calls failing**: Check network connectivity and API endpoints
4. **File download issues**: Ensure proper file paths and permissions

### Debug Mode
Enable debug mode in `Config.ts` for detailed logging:
```typescript
DEBUG: true,
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation and wiki

---

Built with ❤️ for government finance management