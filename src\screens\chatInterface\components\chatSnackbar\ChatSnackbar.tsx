import React, { useRef, useCallback } from 'react';
import { Animated } from 'react-native';
import { Portal, Snackbar } from 'react-native-paper';
import { Colors } from '../../../../constants/Colors';

export interface ChatSnackbarProps {
  visible: boolean;
  message: string;
  onDismiss: () => void;
}

export const ChatSnackbar: React.FC<ChatSnackbarProps> = ({
  visible,
  message,
  onDismiss,
}) => {
  const snackbarAnim = useRef(new Animated.Value(0)).current;

  const showSnackbar = useCallback((text: string) => {
    // This would be called from parent component
    // Animation logic can be added here if needed
  }, []);

  return (
    <Portal>
      <Snackbar
        visible={visible}
        onDismiss={onDismiss}
        duration={3000}
        style={{
          // backgroundColor: Colors.surface,
          marginBottom: 100,
        }}
        action={{
          label: 'OK',
          onPress: onDismiss,
          textColor: Colors.primary,
        }}
      >
        {message}
      </Snackbar>
    </Portal>
  );
};

export default ChatSnackbar;
