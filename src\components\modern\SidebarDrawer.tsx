/**
 * SidebarDrawer Component
 * ChatGPT-inspired sidebar menu
 */

import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Platform,
  ScrollView,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Typography, Spacing, BorderRadius, Shadows } from '../../theme';
import { BlurView } from 'expo-blur';
import { useTheme } from '../../contexts/ThemeContext';

interface MenuItem {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  badge?: string;
}

interface SidebarDrawerProps {
  visible: boolean;
  onClose: () => void;
  menuItems: MenuItem[];
  userName?: string;
  userEmail?: string;
}

export const SidebarDrawer = memo<SidebarDrawerProps>(
  ({ visible, onClose, menuItems, userName = 'User', userEmail }) => {
    const { theme, isDark } = useTheme();

    return (
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
      >
        <View style={styles.overlay}>
          {/* Sidebar */}
          <Animated.View style={[styles.sidebar, { backgroundColor: theme.surface }]}>
            <View style={styles.sidebarContent}>
              {/* Header */}
              <View style={[styles.header, { borderBottomColor: theme.border }]}>
                <View style={styles.headerTop}>
                  <Text style={[styles.headerTitle, { color: theme.textPrimary }]}>FinanceGPT</Text>
                  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                    <Ionicons name="close" size={24} color={theme.textPrimary} />
                  </TouchableOpacity>
                </View>

                {/* User Info */}
                <View style={styles.userInfo}>
                  <View style={[styles.userAvatar, { backgroundColor: theme.primary }]}>
                    <Ionicons name="person" size={24} color={theme.textOnPrimary} />
                  </View>
                  <View style={styles.userDetails}>
                    <Text style={[styles.userName, { color: theme.textPrimary }]}>{userName}</Text>
                    {userEmail && <Text style={[styles.userEmail, { color: theme.textSecondary }]}>{userEmail}</Text>}
                  </View>
                </View>
              </View>

              {/* Menu Items */}
              <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
                {menuItems.map((item, index) => (
                  <TouchableOpacity
                    key={item.id}
                    style={[
                      styles.menuItem,
                      index === 0 && styles.firstMenuItem,
                      { borderBottomColor: theme.border },
                    ]}
                    onPress={() => {
                      item.onPress();
                      onClose();
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.menuItemLeft}>
                      <Ionicons name={item.icon} size={22} color={theme.textPrimary} />
                      <Text style={[styles.menuItemText, { color: theme.textPrimary }]}>{item.title}</Text>
                    </View>
                    {item.badge && (
                      <View style={[styles.badge, { backgroundColor: theme.accent }]}>
                        <Text style={[styles.badgeText, { color: theme.textOnPrimary }]}>{item.badge}</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>

              {/* Footer */}
              <View style={[styles.footer, { borderTopColor: theme.border }]}>
                <Text style={[styles.footerText, { color: theme.textSecondary }]}>Version 2.0.0</Text>
                <Text style={[styles.footerSubtext, { color: theme.textMuted }]}>
                  Government of Balochistan
                </Text>
              </View>
            </View>
          </Animated.View>

          {/* Backdrop */}
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={onClose}
          >
            {Platform.OS === 'ios' ? (
              <BlurView intensity={20} style={StyleSheet.absoluteFill} tint={isDark ? 'dark' : 'light'} />
            ) : (
              <View style={styles.androidBackdrop} />
            )}
          </TouchableOpacity>
        </View>
      </Modal>
    );
  }
);

SidebarDrawer.displayName = 'SidebarDrawer';

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  backdrop: {
    flex: 1,
  },
  androidBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sidebar: {
    width: '80%',
    maxWidth: 320,
    ...Platform.select({
      ios: {
        ...Shadows.lg,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  sidebarContent: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
    borderBottomWidth: 1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
  },
  closeButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  userEmail: {
    fontSize: Typography.fontSize.sm,
    marginTop: 2,
  },
  menuContainer: {
    flex: 1,
    paddingTop: Spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderBottomWidth: 1,
  },
  firstMenuItem: {
    borderTopWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    fontSize: Typography.fontSize.base,
    marginLeft: Spacing.md,
    fontWeight: Typography.fontWeight.medium,
  },
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.full,
    minWidth: 24,
    alignItems: 'center',
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: Typography.fontSize.sm,
    textAlign: 'center',
  },
  footerSubtext: {
    fontSize: Typography.fontSize.xs,
    textAlign: 'center',
    marginTop: 4,
  },
});

