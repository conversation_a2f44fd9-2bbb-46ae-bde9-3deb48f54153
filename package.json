{"name": "finance-gpt", "version": "2.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "axios": "^1.12.2", "expo": "~54.0.11", "expo-blur": "~15.0.7", "expo-dev-client": "~6.0.13", "expo-document-picker": "^14.0.7", "expo-file-system": "~19.0.16", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-intent-launcher": "~13.0.7", "expo-linear-gradient": "^15.0.7", "expo-sharing": "^14.0.7", "expo-status-bar": "~3.0.8", "expo-updates": "~29.0.12", "react": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-gifted-chat": "^2.4.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "~4.16.0"}, "devDependencies": {"@types/react": "~19.1.0", "babel-preset-expo": "^54.0.3", "typescript": "~5.9.2"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-file-viewer", "react-native-fs"]}}}}