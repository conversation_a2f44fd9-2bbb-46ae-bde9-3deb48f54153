@echo off
echo ========================================
echo iOS PRODUCTION BUILD - FinanceGPT v1.0.1
echo ========================================
echo.

echo Step 1: Checking EAS CLI installation...
call eas --version
if errorlevel 1 (
    echo EAS CLI not found. Installing...
    call npm install -g eas-cli
)

echo.
echo Step 2: Logging into Expo account...
call eas login

echo.
echo Step 3: Building iOS production app...
echo This will create an IPA file for App Store submission
echo.
call eas build --platform ios --profile production

echo.
echo ========================================
echo BUILD COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Download the IPA from Expo dashboard
echo 2. Go to https://appstoreconnect.apple.com
echo 3. Create a new app listing
echo 4. Upload the IPA using Transporter app
echo 5. Submit for review
echo.
echo Or use automatic submission:
echo   eas submit --platform ios --profile production
echo.
pause

