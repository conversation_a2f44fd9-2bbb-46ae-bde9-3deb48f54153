const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Force Metro to bind to network interface for development
config.server = {
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Enable CORS for development
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      return middleware(req, res, next);
    };
  },
};

// Enable network access for development builds
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;